/*
THIS IS A GENERATED/BUNDLED FILE BY ESBUILD
if you want to view the source, please visit the github repository of this plugin
*/

var __create = Object.create;
var __defProp = Object.defineProperty;
var __getOwnPropDesc = Object.getOwnPropertyDescriptor;
var __getOwnPropNames = Object.getOwnPropertyNames;
var __getProtoOf = Object.getPrototypeOf;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __commonJS = (cb, mod) => function __require() {
  return mod || (0, cb[__getOwnPropNames(cb)[0]])((mod = { exports: {} }).exports, mod), mod.exports;
};
var __export = (target, all) => {
  for (var name in all)
    __defProp(target, name, { get: all[name], enumerable: true });
};
var __copyProps = (to, from, except, desc) => {
  if (from && typeof from === "object" || typeof from === "function") {
    for (let key of __getOwnPropNames(from))
      if (!__hasOwnProp.call(to, key) && key !== except)
        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });
  }
  return to;
};
var __toESM = (mod, isNodeMode, target) => (target = mod != null ? __create(__getProtoOf(mod)) : {}, __copyProps(
  // If the importer is in node compatibility mode or this is not an ESM
  // file that has been converted to a CommonJS file using a Babel-
  // compatible transform (i.e. "__esModule" has not been set), then set
  // "default" to the CommonJS "module.exports" for node compatibility.
  isNodeMode || !mod || !mod.__esModule ? __defProp(target, "default", { value: mod, enumerable: true }) : target,
  mod
));
var __toCommonJS = (mod) => __copyProps(__defProp({}, "__esModule", { value: true }), mod);

// node_modules/turndown/lib/turndown.browser.cjs.js
var require_turndown_browser_cjs = __commonJS({
  "node_modules/turndown/lib/turndown.browser.cjs.js"(exports, module2) {
    "use strict";
    function extend(destination) {
      for (var i = 1; i < arguments.length; i++) {
        var source = arguments[i];
        for (var key in source) {
          if (source.hasOwnProperty(key))
            destination[key] = source[key];
        }
      }
      return destination;
    }
    function repeat(character, count) {
      return Array(count + 1).join(character);
    }
    function trimLeadingNewlines(string) {
      return string.replace(/^\n*/, "");
    }
    function trimTrailingNewlines(string) {
      var indexEnd = string.length;
      while (indexEnd > 0 && string[indexEnd - 1] === "\n")
        indexEnd--;
      return string.substring(0, indexEnd);
    }
    var blockElements = [
      "ADDRESS",
      "ARTICLE",
      "ASIDE",
      "AUDIO",
      "BLOCKQUOTE",
      "BODY",
      "CANVAS",
      "CENTER",
      "DD",
      "DIR",
      "DIV",
      "DL",
      "DT",
      "FIELDSET",
      "FIGCAPTION",
      "FIGURE",
      "FOOTER",
      "FORM",
      "FRAMESET",
      "H1",
      "H2",
      "H3",
      "H4",
      "H5",
      "H6",
      "HEADER",
      "HGROUP",
      "HR",
      "HTML",
      "ISINDEX",
      "LI",
      "MAIN",
      "MENU",
      "NAV",
      "NOFRAMES",
      "NOSCRIPT",
      "OL",
      "OUTPUT",
      "P",
      "PRE",
      "SECTION",
      "TABLE",
      "TBODY",
      "TD",
      "TFOOT",
      "TH",
      "THEAD",
      "TR",
      "UL"
    ];
    function isBlock(node) {
      return is(node, blockElements);
    }
    var voidElements = [
      "AREA",
      "BASE",
      "BR",
      "COL",
      "COMMAND",
      "EMBED",
      "HR",
      "IMG",
      "INPUT",
      "KEYGEN",
      "LINK",
      "META",
      "PARAM",
      "SOURCE",
      "TRACK",
      "WBR"
    ];
    function isVoid(node) {
      return is(node, voidElements);
    }
    function hasVoid(node) {
      return has(node, voidElements);
    }
    var meaningfulWhenBlankElements = [
      "A",
      "TABLE",
      "THEAD",
      "TBODY",
      "TFOOT",
      "TH",
      "TD",
      "IFRAME",
      "SCRIPT",
      "AUDIO",
      "VIDEO"
    ];
    function isMeaningfulWhenBlank(node) {
      return is(node, meaningfulWhenBlankElements);
    }
    function hasMeaningfulWhenBlank(node) {
      return has(node, meaningfulWhenBlankElements);
    }
    function is(node, tagNames) {
      return tagNames.indexOf(node.nodeName) >= 0;
    }
    function has(node, tagNames) {
      return node.getElementsByTagName && tagNames.some(function(tagName) {
        return node.getElementsByTagName(tagName).length;
      });
    }
    var rules = {};
    rules.paragraph = {
      filter: "p",
      replacement: function(content) {
        return "\n\n" + content + "\n\n";
      }
    };
    rules.lineBreak = {
      filter: "br",
      replacement: function(content, node, options) {
        return options.br + "\n";
      }
    };
    rules.heading = {
      filter: ["h1", "h2", "h3", "h4", "h5", "h6"],
      replacement: function(content, node, options) {
        var hLevel = Number(node.nodeName.charAt(1));
        if (options.headingStyle === "setext" && hLevel < 3) {
          var underline = repeat(hLevel === 1 ? "=" : "-", content.length);
          return "\n\n" + content + "\n" + underline + "\n\n";
        } else {
          return "\n\n" + repeat("#", hLevel) + " " + content + "\n\n";
        }
      }
    };
    rules.blockquote = {
      filter: "blockquote",
      replacement: function(content) {
        content = content.replace(/^\n+|\n+$/g, "");
        content = content.replace(/^/gm, "> ");
        return "\n\n" + content + "\n\n";
      }
    };
    rules.list = {
      filter: ["ul", "ol"],
      replacement: function(content, node) {
        var parent = node.parentNode;
        if (parent.nodeName === "LI" && parent.lastElementChild === node) {
          return "\n" + content;
        } else {
          return "\n\n" + content + "\n\n";
        }
      }
    };
    rules.listItem = {
      filter: "li",
      replacement: function(content, node, options) {
        content = content.replace(/^\n+/, "").replace(/\n+$/, "\n").replace(/\n/gm, "\n    ");
        var prefix = options.bulletListMarker + "   ";
        var parent = node.parentNode;
        if (parent.nodeName === "OL") {
          var start = parent.getAttribute("start");
          var index = Array.prototype.indexOf.call(parent.children, node);
          prefix = (start ? Number(start) + index : index + 1) + ".  ";
        }
        return prefix + content + (node.nextSibling && !/\n$/.test(content) ? "\n" : "");
      }
    };
    rules.indentedCodeBlock = {
      filter: function(node, options) {
        return options.codeBlockStyle === "indented" && node.nodeName === "PRE" && node.firstChild && node.firstChild.nodeName === "CODE";
      },
      replacement: function(content, node, options) {
        return "\n\n    " + node.firstChild.textContent.replace(/\n/g, "\n    ") + "\n\n";
      }
    };
    rules.fencedCodeBlock = {
      filter: function(node, options) {
        return options.codeBlockStyle === "fenced" && node.nodeName === "PRE" && node.firstChild && node.firstChild.nodeName === "CODE";
      },
      replacement: function(content, node, options) {
        var className = node.firstChild.getAttribute("class") || "";
        var language = (className.match(/language-(\S+)/) || [null, ""])[1];
        var code = node.firstChild.textContent;
        var fenceChar = options.fence.charAt(0);
        var fenceSize = 3;
        var fenceInCodeRegex = new RegExp("^" + fenceChar + "{3,}", "gm");
        var match;
        while (match = fenceInCodeRegex.exec(code)) {
          if (match[0].length >= fenceSize) {
            fenceSize = match[0].length + 1;
          }
        }
        var fence = repeat(fenceChar, fenceSize);
        return "\n\n" + fence + language + "\n" + code.replace(/\n$/, "") + "\n" + fence + "\n\n";
      }
    };
    rules.horizontalRule = {
      filter: "hr",
      replacement: function(content, node, options) {
        return "\n\n" + options.hr + "\n\n";
      }
    };
    rules.inlineLink = {
      filter: function(node, options) {
        return options.linkStyle === "inlined" && node.nodeName === "A" && node.getAttribute("href");
      },
      replacement: function(content, node) {
        var href = node.getAttribute("href");
        if (href)
          href = href.replace(/([()])/g, "\\$1");
        var title = cleanAttribute(node.getAttribute("title"));
        if (title)
          title = ' "' + title.replace(/"/g, '\\"') + '"';
        return "[" + content + "](" + href + title + ")";
      }
    };
    rules.referenceLink = {
      filter: function(node, options) {
        return options.linkStyle === "referenced" && node.nodeName === "A" && node.getAttribute("href");
      },
      replacement: function(content, node, options) {
        var href = node.getAttribute("href");
        var title = cleanAttribute(node.getAttribute("title"));
        if (title)
          title = ' "' + title + '"';
        var replacement;
        var reference;
        switch (options.linkReferenceStyle) {
          case "collapsed":
            replacement = "[" + content + "][]";
            reference = "[" + content + "]: " + href + title;
            break;
          case "shortcut":
            replacement = "[" + content + "]";
            reference = "[" + content + "]: " + href + title;
            break;
          default:
            var id = this.references.length + 1;
            replacement = "[" + content + "][" + id + "]";
            reference = "[" + id + "]: " + href + title;
        }
        this.references.push(reference);
        return replacement;
      },
      references: [],
      append: function(options) {
        var references = "";
        if (this.references.length) {
          references = "\n\n" + this.references.join("\n") + "\n\n";
          this.references = [];
        }
        return references;
      }
    };
    rules.emphasis = {
      filter: ["em", "i"],
      replacement: function(content, node, options) {
        if (!content.trim())
          return "";
        return options.emDelimiter + content + options.emDelimiter;
      }
    };
    rules.strong = {
      filter: ["strong", "b"],
      replacement: function(content, node, options) {
        if (!content.trim())
          return "";
        return options.strongDelimiter + content + options.strongDelimiter;
      }
    };
    rules.code = {
      filter: function(node) {
        var hasSiblings = node.previousSibling || node.nextSibling;
        var isCodeBlock = node.parentNode.nodeName === "PRE" && !hasSiblings;
        return node.nodeName === "CODE" && !isCodeBlock;
      },
      replacement: function(content) {
        if (!content)
          return "";
        content = content.replace(/\r?\n|\r/g, " ");
        var extraSpace = /^`|^ .*?[^ ].* $|`$/.test(content) ? " " : "";
        var delimiter = "`";
        var matches = content.match(/`+/gm) || [];
        while (matches.indexOf(delimiter) !== -1)
          delimiter = delimiter + "`";
        return delimiter + extraSpace + content + extraSpace + delimiter;
      }
    };
    rules.image = {
      filter: "img",
      replacement: function(content, node) {
        var alt = cleanAttribute(node.getAttribute("alt"));
        var src = node.getAttribute("src") || "";
        var title = cleanAttribute(node.getAttribute("title"));
        var titlePart = title ? ' "' + title + '"' : "";
        return src ? "![" + alt + "](" + src + titlePart + ")" : "";
      }
    };
    function cleanAttribute(attribute) {
      return attribute ? attribute.replace(/(\n+\s*)+/g, "\n") : "";
    }
    function Rules(options) {
      this.options = options;
      this._keep = [];
      this._remove = [];
      this.blankRule = {
        replacement: options.blankReplacement
      };
      this.keepReplacement = options.keepReplacement;
      this.defaultRule = {
        replacement: options.defaultReplacement
      };
      this.array = [];
      for (var key in options.rules)
        this.array.push(options.rules[key]);
    }
    Rules.prototype = {
      add: function(key, rule) {
        this.array.unshift(rule);
      },
      keep: function(filter) {
        this._keep.unshift({
          filter,
          replacement: this.keepReplacement
        });
      },
      remove: function(filter) {
        this._remove.unshift({
          filter,
          replacement: function() {
            return "";
          }
        });
      },
      forNode: function(node) {
        if (node.isBlank)
          return this.blankRule;
        var rule;
        if (rule = findRule(this.array, node, this.options))
          return rule;
        if (rule = findRule(this._keep, node, this.options))
          return rule;
        if (rule = findRule(this._remove, node, this.options))
          return rule;
        return this.defaultRule;
      },
      forEach: function(fn) {
        for (var i = 0; i < this.array.length; i++)
          fn(this.array[i], i);
      }
    };
    function findRule(rules2, node, options) {
      for (var i = 0; i < rules2.length; i++) {
        var rule = rules2[i];
        if (filterValue(rule, node, options))
          return rule;
      }
      return void 0;
    }
    function filterValue(rule, node, options) {
      var filter = rule.filter;
      if (typeof filter === "string") {
        if (filter === node.nodeName.toLowerCase())
          return true;
      } else if (Array.isArray(filter)) {
        if (filter.indexOf(node.nodeName.toLowerCase()) > -1)
          return true;
      } else if (typeof filter === "function") {
        if (filter.call(rule, node, options))
          return true;
      } else {
        throw new TypeError("`filter` needs to be a string, array, or function");
      }
    }
    function collapseWhitespace(options) {
      var element = options.element;
      var isBlock2 = options.isBlock;
      var isVoid2 = options.isVoid;
      var isPre = options.isPre || function(node2) {
        return node2.nodeName === "PRE";
      };
      if (!element.firstChild || isPre(element))
        return;
      var prevText = null;
      var keepLeadingWs = false;
      var prev = null;
      var node = next(prev, element, isPre);
      while (node !== element) {
        if (node.nodeType === 3 || node.nodeType === 4) {
          var text = node.data.replace(/[ \r\n\t]+/g, " ");
          if ((!prevText || / $/.test(prevText.data)) && !keepLeadingWs && text[0] === " ") {
            text = text.substr(1);
          }
          if (!text) {
            node = remove(node);
            continue;
          }
          node.data = text;
          prevText = node;
        } else if (node.nodeType === 1) {
          if (isBlock2(node) || node.nodeName === "BR") {
            if (prevText) {
              prevText.data = prevText.data.replace(/ $/, "");
            }
            prevText = null;
            keepLeadingWs = false;
          } else if (isVoid2(node) || isPre(node)) {
            prevText = null;
            keepLeadingWs = true;
          } else if (prevText) {
            keepLeadingWs = false;
          }
        } else {
          node = remove(node);
          continue;
        }
        var nextNode = next(prev, node, isPre);
        prev = node;
        node = nextNode;
      }
      if (prevText) {
        prevText.data = prevText.data.replace(/ $/, "");
        if (!prevText.data) {
          remove(prevText);
        }
      }
    }
    function remove(node) {
      var next2 = node.nextSibling || node.parentNode;
      node.parentNode.removeChild(node);
      return next2;
    }
    function next(prev, current, isPre) {
      if (prev && prev.parentNode === current || isPre(current)) {
        return current.nextSibling || current.parentNode;
      }
      return current.firstChild || current.nextSibling || current.parentNode;
    }
    var root = typeof window !== "undefined" ? window : {};
    function canParseHTMLNatively() {
      var Parser = root.DOMParser;
      var canParse = false;
      try {
        if (new Parser().parseFromString("", "text/html")) {
          canParse = true;
        }
      } catch (e) {
      }
      return canParse;
    }
    function createHTMLParser() {
      var Parser = function() {
      };
      {
        if (shouldUseActiveX()) {
          Parser.prototype.parseFromString = function(string) {
            var doc = new window.ActiveXObject("htmlfile");
            doc.designMode = "on";
            doc.open();
            doc.write(string);
            doc.close();
            return doc;
          };
        } else {
          Parser.prototype.parseFromString = function(string) {
            var doc = document.implementation.createHTMLDocument("");
            doc.open();
            doc.write(string);
            doc.close();
            return doc;
          };
        }
      }
      return Parser;
    }
    function shouldUseActiveX() {
      var useActiveX = false;
      try {
        document.implementation.createHTMLDocument("").open();
      } catch (e) {
        if (root.ActiveXObject)
          useActiveX = true;
      }
      return useActiveX;
    }
    var HTMLParser = canParseHTMLNatively() ? root.DOMParser : createHTMLParser();
    function RootNode(input, options) {
      var root2;
      if (typeof input === "string") {
        var doc = htmlParser().parseFromString(
          // DOM parsers arrange elements in the <head> and <body>.
          // Wrapping in a custom element ensures elements are reliably arranged in
          // a single element.
          '<x-turndown id="turndown-root">' + input + "</x-turndown>",
          "text/html"
        );
        root2 = doc.getElementById("turndown-root");
      } else {
        root2 = input.cloneNode(true);
      }
      collapseWhitespace({
        element: root2,
        isBlock,
        isVoid,
        isPre: options.preformattedCode ? isPreOrCode : null
      });
      return root2;
    }
    var _htmlParser;
    function htmlParser() {
      _htmlParser = _htmlParser || new HTMLParser();
      return _htmlParser;
    }
    function isPreOrCode(node) {
      return node.nodeName === "PRE" || node.nodeName === "CODE";
    }
    function Node(node, options) {
      node.isBlock = isBlock(node);
      node.isCode = node.nodeName === "CODE" || node.parentNode.isCode;
      node.isBlank = isBlank(node);
      node.flankingWhitespace = flankingWhitespace(node, options);
      return node;
    }
    function isBlank(node) {
      return !isVoid(node) && !isMeaningfulWhenBlank(node) && /^\s*$/i.test(node.textContent) && !hasVoid(node) && !hasMeaningfulWhenBlank(node);
    }
    function flankingWhitespace(node, options) {
      if (node.isBlock || options.preformattedCode && node.isCode) {
        return { leading: "", trailing: "" };
      }
      var edges = edgeWhitespace(node.textContent);
      if (edges.leadingAscii && isFlankedByWhitespace("left", node, options)) {
        edges.leading = edges.leadingNonAscii;
      }
      if (edges.trailingAscii && isFlankedByWhitespace("right", node, options)) {
        edges.trailing = edges.trailingNonAscii;
      }
      return { leading: edges.leading, trailing: edges.trailing };
    }
    function edgeWhitespace(string) {
      var m = string.match(/^(([ \t\r\n]*)(\s*))(?:(?=\S)[\s\S]*\S)?((\s*?)([ \t\r\n]*))$/);
      return {
        leading: m[1],
        // whole string for whitespace-only strings
        leadingAscii: m[2],
        leadingNonAscii: m[3],
        trailing: m[4],
        // empty for whitespace-only strings
        trailingNonAscii: m[5],
        trailingAscii: m[6]
      };
    }
    function isFlankedByWhitespace(side, node, options) {
      var sibling;
      var regExp;
      var isFlanked;
      if (side === "left") {
        sibling = node.previousSibling;
        regExp = / $/;
      } else {
        sibling = node.nextSibling;
        regExp = /^ /;
      }
      if (sibling) {
        if (sibling.nodeType === 3) {
          isFlanked = regExp.test(sibling.nodeValue);
        } else if (options.preformattedCode && sibling.nodeName === "CODE") {
          isFlanked = false;
        } else if (sibling.nodeType === 1 && !isBlock(sibling)) {
          isFlanked = regExp.test(sibling.textContent);
        }
      }
      return isFlanked;
    }
    var reduce = Array.prototype.reduce;
    var escapes = [
      [/\\/g, "\\\\"],
      [/\*/g, "\\*"],
      [/^-/g, "\\-"],
      [/^\+ /g, "\\+ "],
      [/^(=+)/g, "\\$1"],
      [/^(#{1,6}) /g, "\\$1 "],
      [/`/g, "\\`"],
      [/^~~~/g, "\\~~~"],
      [/\[/g, "\\["],
      [/\]/g, "\\]"],
      [/^>/g, "\\>"],
      [/_/g, "\\_"],
      [/^(\d+)\. /g, "$1\\. "]
    ];
    function TurndownService3(options) {
      if (!(this instanceof TurndownService3))
        return new TurndownService3(options);
      var defaults = {
        rules,
        headingStyle: "setext",
        hr: "* * *",
        bulletListMarker: "*",
        codeBlockStyle: "indented",
        fence: "```",
        emDelimiter: "_",
        strongDelimiter: "**",
        linkStyle: "inlined",
        linkReferenceStyle: "full",
        br: "  ",
        preformattedCode: false,
        blankReplacement: function(content, node) {
          return node.isBlock ? "\n\n" : "";
        },
        keepReplacement: function(content, node) {
          return node.isBlock ? "\n\n" + node.outerHTML + "\n\n" : node.outerHTML;
        },
        defaultReplacement: function(content, node) {
          return node.isBlock ? "\n\n" + content + "\n\n" : content;
        }
      };
      this.options = extend({}, defaults, options);
      this.rules = new Rules(this.options);
    }
    TurndownService3.prototype = {
      /**
       * The entry point for converting a string or DOM node to Markdown
       * @public
       * @param {String|HTMLElement} input The string or DOM node to convert
       * @returns A Markdown representation of the input
       * @type String
       */
      turndown: function(input) {
        if (!canConvert(input)) {
          throw new TypeError(
            input + " is not a string, or an element/document/fragment node."
          );
        }
        if (input === "")
          return "";
        var output = process.call(this, new RootNode(input, this.options));
        return postProcess.call(this, output);
      },
      /**
       * Add one or more plugins
       * @public
       * @param {Function|Array} plugin The plugin or array of plugins to add
       * @returns The Turndown instance for chaining
       * @type Object
       */
      use: function(plugin) {
        if (Array.isArray(plugin)) {
          for (var i = 0; i < plugin.length; i++)
            this.use(plugin[i]);
        } else if (typeof plugin === "function") {
          plugin(this);
        } else {
          throw new TypeError("plugin must be a Function or an Array of Functions");
        }
        return this;
      },
      /**
       * Adds a rule
       * @public
       * @param {String} key The unique key of the rule
       * @param {Object} rule The rule
       * @returns The Turndown instance for chaining
       * @type Object
       */
      addRule: function(key, rule) {
        this.rules.add(key, rule);
        return this;
      },
      /**
       * Keep a node (as HTML) that matches the filter
       * @public
       * @param {String|Array|Function} filter The unique key of the rule
       * @returns The Turndown instance for chaining
       * @type Object
       */
      keep: function(filter) {
        this.rules.keep(filter);
        return this;
      },
      /**
       * Remove a node that matches the filter
       * @public
       * @param {String|Array|Function} filter The unique key of the rule
       * @returns The Turndown instance for chaining
       * @type Object
       */
      remove: function(filter) {
        this.rules.remove(filter);
        return this;
      },
      /**
       * Escapes Markdown syntax
       * @public
       * @param {String} string The string to escape
       * @returns A string with Markdown syntax escaped
       * @type String
       */
      escape: function(string) {
        return escapes.reduce(function(accumulator, escape) {
          return accumulator.replace(escape[0], escape[1]);
        }, string);
      }
    };
    function process(parentNode) {
      var self = this;
      return reduce.call(parentNode.childNodes, function(output, node) {
        node = new Node(node, self.options);
        var replacement = "";
        if (node.nodeType === 3) {
          replacement = node.isCode ? node.nodeValue : self.escape(node.nodeValue);
        } else if (node.nodeType === 1) {
          replacement = replacementForNode.call(self, node);
        }
        return join(output, replacement);
      }, "");
    }
    function postProcess(output) {
      var self = this;
      this.rules.forEach(function(rule) {
        if (typeof rule.append === "function") {
          output = join(output, rule.append(self.options));
        }
      });
      return output.replace(/^[\t\r\n]+/, "").replace(/[\t\r\n\s]+$/, "");
    }
    function replacementForNode(node) {
      var rule = this.rules.forNode(node);
      var content = process.call(this, node);
      var whitespace = node.flankingWhitespace;
      if (whitespace.leading || whitespace.trailing)
        content = content.trim();
      return whitespace.leading + rule.replacement(content, node, this.options) + whitespace.trailing;
    }
    function join(output, replacement) {
      var s1 = trimTrailingNewlines(output);
      var s2 = trimLeadingNewlines(replacement);
      var nls = Math.max(output.length - s1.length, replacement.length - s2.length);
      var separator = "\n\n".substring(0, nls);
      return s1 + separator + s2;
    }
    function canConvert(input) {
      return input != null && (typeof input === "string" || input.nodeType && (input.nodeType === 1 || input.nodeType === 9 || input.nodeType === 11));
    }
    module2.exports = TurndownService3;
  }
});

// src/main.ts
var main_exports = {};
__export(main_exports, {
  default: () => GhostSyncPlugin
});
module.exports = __toCommonJS(main_exports);
var import_obsidian4 = require("obsidian");
var path2 = __toESM(require("path"));

// src/utils/content-converter.ts
var TurndownService = require_turndown_browser_cjs();
var ContentConverter = class {
  static htmlToMarkdown(html) {
    if (!html)
      return "";
    const turndownService = new TurndownService({
      headingStyle: "atx",
      codeBlockStyle: "fenced",
      fence: "```",
      emDelimiter: "*",
      strongDelimiter: "**",
      linkStyle: "inlined",
      linkReferenceStyle: "full"
    });
    turndownService.addRule("codeBlock", {
      filter: function(node) {
        return node.nodeName === "PRE" && node.firstChild && node.firstChild.nodeName === "CODE";
      },
      replacement: function(content, node) {
        const codeElement = node.firstChild;
        const className = codeElement.className || "";
        const languageMatch = className.match(/language-(\w+)/);
        const language = languageMatch ? languageMatch[1] : "";
        const cleanContent = content.trim();
        return "\n\n```" + language + "\n" + cleanContent + "\n```\n\n";
      }
    });
    return turndownService.turndown(html);
  }
  static createFilename(title) {
    return title.toLowerCase().replace(/[^\w\s-]/g, "").replace(/\s+/g, "-").replace(/-+/g, "-").trim();
  }
  static slugify(text) {
    return text.toLowerCase().replace(/[^\w\s-]/g, "").replace(/\s+/g, "-").replace(/-+/g, "-").trim();
  }
  static normalizeFrontMatter(frontMatter) {
    const propertyMap = {
      "Title": "title",
      "Slug": "slug",
      "Status": "status",
      "Created At": "created_at",
      "Updated At": "updated_at",
      "Published At": "published_at",
      "Date": "published_at",
      // Map legacy "Date" to "Published At"
      "Tags": "tags",
      "Featured Image": "feature_image",
      "Featured": "featured",
      "Visibility": "visibility"
    };
    const normalized = {};
    for (const [key, value] of Object.entries(frontMatter)) {
      const mappedKey = propertyMap[key] || key.toLowerCase();
      if (key.toLowerCase() === "date") {
        normalized["published_at"] = value;
      } else {
        normalized[mappedKey] = value;
      }
    }
    return normalized;
  }
  static createGhostPostData(frontMatter, markdownContent, options = {}) {
    const { status = "draft", isUpdate = false, existingPost = null } = options;
    const normalizedFrontMatter = this.normalizeFrontMatter(frontMatter);
    const slug = normalizedFrontMatter.slug || this.slugify(normalizedFrontMatter.title);
    const newStatus = normalizedFrontMatter.status || status;
    const postData = {
      title: normalizedFrontMatter.title,
      slug,
      feature_image: normalizedFrontMatter.feature_image || normalizedFrontMatter.image || null,
      featured: normalizedFrontMatter.featured || false,
      status: newStatus,
      visibility: normalizedFrontMatter.visibility || "public",
      custom_excerpt: this.generateExcerpt(normalizedFrontMatter, markdownContent)
    };
    if (isUpdate && existingPost) {
      const existingStatus = existingPost.status;
      const existingPublishedAt = existingPost.published_at;
      if (existingStatus === "draft" && newStatus === "published") {
        postData.published_at = new Date().toISOString();
      } else if (existingPublishedAt) {
        postData.published_at = existingPublishedAt;
      } else if (newStatus === "published") {
        postData.published_at = new Date().toISOString();
      }
    } else {
      if (newStatus === "published") {
        const postDate = this.parseDate(normalizedFrontMatter.published_at) || this.parseDate(normalizedFrontMatter.date) || new Date();
        postData.published_at = postDate.toISOString();
      }
    }
    const htmlContent = this.markdownToHtml(markdownContent);
    if (!htmlContent || htmlContent.trim() === "" || htmlContent.trim() === "<p></p>") {
      postData.html = "<p>Content is being updated...</p>";
    } else {
      postData.html = htmlContent;
    }
    if (isUpdate) {
    } else {
      postData.lexical = null;
    }
    postData.mobiledoc = null;
    return postData;
  }
  static parseDate(dateStr) {
    if (!dateStr)
      return null;
    const date = new Date(dateStr);
    return isNaN(date.getTime()) ? null : date;
  }
  static generateExcerpt(frontMatter, content) {
    if (content) {
      const plaintext = content.replace(/[#*`_\[\]()]/g, "").trim();
      if (plaintext.length <= 300) {
        return plaintext;
      }
      const truncated = plaintext.substring(0, 297);
      const lastSpace = truncated.lastIndexOf(" ");
      if (lastSpace > 250) {
        return truncated.substring(0, lastSpace) + "...";
      }
      return truncated + "...";
    }
    return null;
  }
  static markdownToHtml(markdown) {
    if (!markdown || markdown.trim() === "") {
      return "<p></p>";
    }
    const codeBlocks = [];
    let html = markdown.replace(/```(\w+)?\n([\s\S]*?)```/g, (_match, lang, code) => {
      const placeholder = `__CODE_BLOCK_${codeBlocks.length}__`;
      const langClass = lang ? ` class="language-${lang}"` : "";
      codeBlocks.push(`<pre><code${langClass}>${this.escapeHtml(code.trim())}</code></pre>`);
      return placeholder;
    });
    html = html.replace(/`([^`]+)`/g, "<code>$1</code>");
    html = html.replace(/^### (.*$)/gm, "<h3>$1</h3>");
    html = html.replace(/^## (.*$)/gm, "<h2>$1</h2>");
    html = html.replace(/^# (.*$)/gm, "<h1>$1</h1>");
    html = html.replace(/\*\*(.*?)\*\*/g, "<strong>$1</strong>");
    html = html.replace(/\*(.*?)\*/g, "<em>$1</em>");
    html = html.replace(/\[([^\]]+)\]\(([^)]+)\)/g, '<a href="$2">$1</a>');
    html = html.replace(/\n\n/g, "</p><p>");
    html = html.replace(/\n/g, "<br>");
    if (html.trim()) {
      html = `<p>${html}</p>`;
    } else {
      html = "<p></p>";
    }
    html = html.replace(/<p><\/p>/g, "");
    html = html.replace(/<p><br><\/p>/g, "");
    codeBlocks.forEach((block, index) => {
      html = html.replace(`__CODE_BLOCK_${index}__`, block);
    });
    return html || "<p></p>";
  }
  static escapeHtml(text) {
    const div = document.createElement("div");
    div.textContent = text;
    return div.innerHTML;
  }
  static parseArticle(content) {
    const frontMatterMatch = content.match(/^---\n([\s\S]*?)\n---\n([\s\S]*)$/);
    if (!frontMatterMatch) {
      throw new Error("No front matter found in article");
    }
    const frontMatter = this.parseYaml(frontMatterMatch[1]);
    const markdownContent = frontMatterMatch[2].trim();
    return { frontMatter, markdownContent };
  }
  static parseYaml(yamlString) {
    const result = {};
    const lines = yamlString.split("\n");
    for (const line of lines) {
      const trimmed = line.trim();
      if (!trimmed || trimmed.startsWith("#"))
        continue;
      if (trimmed.includes(":")) {
        const [key, ...valueParts] = trimmed.split(":");
        const value = valueParts.join(":").trim();
        if (value.startsWith('"') && value.endsWith('"')) {
          result[key.trim()] = value.slice(1, -1);
        } else if (value.startsWith("[") && value.endsWith("]")) {
          result[key.trim()] = value.slice(1, -1).split(",").map((s) => s.trim());
        } else if (value === "true") {
          result[key.trim()] = true;
        } else if (value === "false") {
          result[key.trim()] = false;
        } else if (!isNaN(Number(value))) {
          result[key.trim()] = Number(value);
        } else {
          result[key.trim()] = value;
        }
      }
    }
    return result;
  }
  static objectToYaml(obj) {
    let yaml = "";
    for (const [key, value] of Object.entries(obj)) {
      if (Array.isArray(value)) {
        yaml += `${key}:
`;
        for (const item of value) {
          yaml += `  - ${item}
`;
        }
      } else if (typeof value === "string") {
        yaml += `${key}: "${value}"
`;
      } else {
        yaml += `${key}: ${value}
`;
      }
    }
    return yaml;
  }
  static convertGhostPostToArticle(post) {
    const tags = post.tags ? post.tags.map((tag) => tag.name) : [];
    const createdDate = post.created_at ? new Date(post.created_at) : new Date();
    const updatedDate = post.updated_at ? new Date(post.updated_at) : new Date();
    const publishedDate = post.published_at ? new Date(post.published_at) : null;
    const frontmatter = {};
    frontmatter["Title"] = post.title;
    frontmatter["Slug"] = post.slug;
    frontmatter["Status"] = post.status || "draft";
    if (publishedDate) {
      frontmatter["Published At"] = publishedDate.toISOString();
    }
    frontmatter["Created At"] = createdDate.toISOString();
    frontmatter["Updated At"] = updatedDate.toISOString();
    frontmatter["Tags"] = tags;
    if (post.feature_image) {
      frontmatter["Featured Image"] = post.feature_image;
    }
    if (post.featured) {
      frontmatter["Featured"] = post.featured;
    }
    if (post.visibility && post.visibility !== "public") {
      frontmatter["Visibility"] = post.visibility;
    }
    let content = "";
    if (post.lexical) {
      try {
        console.log(`\u2705 LEXICAL PROCESSING: Extracting content for "${post.title}"`);
        const lexicalDoc = JSON.parse(post.lexical);
        const markdownCard = this.extractMarkdownFromLexical(lexicalDoc);
        if (markdownCard) {
          console.log(`\u2705 MARKDOWN EXTRACTED: Found markdown card in lexical`);
          content = markdownCard;
        } else {
          console.log(`\u26A0\uFE0F NO MARKDOWN CARD: Converting HTML to markdown`);
          content = this.htmlToMarkdown(post.html);
        }
      } catch (error) {
        console.warn(`\u26A0\uFE0F LEXICAL ERROR: Failed to process lexical for "${post.title}", using HTML`);
        content = this.htmlToMarkdown(post.html);
      }
    } else if (post.html) {
      console.log(`\u2705 HTML \u2192 Markdown conversion for "${post.title}"`);
      content = this.htmlToMarkdown(post.html);
    } else {
      console.error(`\u274C NO CONTENT: Post "${post.title}" has no lexical or HTML content`);
      content = "";
    }
    const yamlFrontmatter = this.objectToYaml(frontmatter);
    return `---
${yamlFrontmatter}---

${content}`;
  }
  static extractMarkdownFromLexical(lexicalDoc) {
    var _a;
    try {
      if ((_a = lexicalDoc == null ? void 0 : lexicalDoc.root) == null ? void 0 : _a.children) {
        for (const child of lexicalDoc.root.children) {
          if (child.type === "markdown" && child.markdown) {
            return child.markdown;
          }
        }
      }
      return null;
    } catch (error) {
      console.warn("Failed to extract markdown from lexical:", error);
      return null;
    }
  }
};

// src/api/ghost-api.ts
var import_obsidian = require("obsidian");
var ObsidianGhostAPI = class {
  constructor(url, key) {
    this.url = url.replace(/\/$/, "");
    this.key = key;
  }
  async request(endpoint, options = {}) {
    const [id, secret] = this.key.split(":");
    if (!id || !secret) {
      throw new Error("Invalid Ghost Admin API key format. Expected format: id:secret");
    }
    const header = {
      alg: "HS256",
      typ: "JWT",
      kid: id
    };
    const now = Math.floor(Date.now() / 1e3);
    const payload = {
      iat: now,
      exp: now + 300,
      // 5 minutes
      aud: "/admin/"
    };
    const token = await this.createJWT(header, payload, secret);
    const url = `${this.url}/ghost/api/admin/${endpoint}`;
    const requestOptions = {
      url,
      method: options.method || "GET",
      headers: {
        "Authorization": `Ghost ${token}`,
        "Content-Type": "application/json",
        "Accept-Version": "v5.0"
      }
    };
    if (options.body) {
      requestOptions.body = JSON.stringify(options.body);
    }
    const response = await (0, import_obsidian.requestUrl)(requestOptions);
    if (response.status >= 400) {
      throw new Error(`Ghost API error: ${response.status} ${response.text}`);
    }
    return response.json;
  }
  async createJWT(header, payload, secret) {
    const encoder = new TextEncoder();
    const headerB64 = btoa(JSON.stringify(header)).replace(/=/g, "").replace(/\+/g, "-").replace(/\//g, "_");
    const payloadB64 = btoa(JSON.stringify(payload)).replace(/=/g, "").replace(/\+/g, "-").replace(/\//g, "_");
    const data = `${headerB64}.${payloadB64}`;
    const secretBuffer = this.hexToArrayBuffer(secret);
    const key = await crypto.subtle.importKey(
      "raw",
      secretBuffer,
      { name: "HMAC", hash: "SHA-256" },
      false,
      ["sign"]
    );
    const signature = await crypto.subtle.sign("HMAC", key, encoder.encode(data));
    const signatureB64 = btoa(String.fromCharCode(...new Uint8Array(signature))).replace(/=/g, "").replace(/\+/g, "-").replace(/\//g, "_");
    return `${data}.${signatureB64}`;
  }
  hexToArrayBuffer(hex) {
    const bytes = new Uint8Array(hex.length / 2);
    for (let i = 0; i < hex.length; i += 2) {
      bytes[i / 2] = parseInt(hex.substring(i, i + 2), 16);
    }
    return bytes.buffer;
  }
  async getPosts(options = {}) {
    var _a, _b;
    const params = new URLSearchParams({
      limit: ((_a = options.limit) == null ? void 0 : _a.toString()) || "100",
      page: ((_b = options.page) == null ? void 0 : _b.toString()) || "1",
      include: options.include || "tags,authors",
      formats: options.formats || "html,lexical"
    });
    const response = await this.request(`posts/?${params}`);
    return response.posts || [];
  }
  async getPostBySlug(slug) {
    var _a;
    const response = await this.request(`posts/slug/${slug}/?include=tags,authors&formats=html,lexical`);
    return ((_a = response.posts) == null ? void 0 : _a[0]) || null;
  }
  async createPost(postData) {
    var _a;
    const response = await this.request("posts/", {
      method: "POST",
      body: { posts: [postData] }
    });
    return (_a = response.posts) == null ? void 0 : _a[0];
  }
  async updatePost(postData) {
    var _a;
    const response = await this.request(`posts/${postData.id}/`, {
      method: "PUT",
      body: { posts: [postData] }
    });
    return (_a = response.posts) == null ? void 0 : _a[0];
  }
};

// src/views/sync-status-view.ts
var import_obsidian2 = require("obsidian");
var path = __toESM(require("path"));
var VIEW_TYPE_GHOST_SYNC_STATUS = "ghost-sync-status";
var GhostSyncStatusView = class extends import_obsidian2.ItemView {
  constructor(leaf, plugin) {
    super(leaf);
    this.currentFile = null;
    this.syncStatus = {
      title: "unknown",
      slug: "unknown",
      status: "unknown",
      tags: "unknown",
      featured: "unknown",
      created_at: "unknown",
      updated_at: "unknown",
      published_at: "unknown"
    };
    this.plugin = plugin;
  }
  getViewType() {
    return VIEW_TYPE_GHOST_SYNC_STATUS;
  }
  getDisplayText() {
    return "Ghost Sync Status";
  }
  getIcon() {
    return "sync";
  }
  async onOpen() {
    const container = this.contentEl;
    container.empty();
    container.addClass("ghost-sync-status-view");
    this.registerEvent(
      this.app.workspace.on("active-leaf-change", () => {
        this.updateCurrentFile();
      })
    );
    this.registerEvent(
      this.app.workspace.on("file-open", () => {
        this.updateCurrentFile();
      })
    );
    this.registerEvent(
      this.app.vault.on("modify", (file) => {
        if (file === this.currentFile) {
          this.updateSyncStatus();
        }
      })
    );
    this.updateCurrentFile();
    this.render();
  }
  async onClose() {
  }
  updateCurrentFile() {
    let newFile = null;
    const activeEditor = this.app.workspace.activeEditor;
    if (activeEditor == null ? void 0 : activeEditor.file) {
      newFile = activeEditor.file;
    } else {
      const activeView = this.app.workspace.getActiveViewOfType(import_obsidian2.MarkdownView);
      if (activeView == null ? void 0 : activeView.file) {
        newFile = activeView.file;
      } else {
        const markdownLeaves = this.app.workspace.getLeavesOfType("markdown");
        for (const leaf of markdownLeaves) {
          if (leaf.view instanceof import_obsidian2.MarkdownView && leaf.view.file) {
            newFile = leaf.view.file;
            break;
          }
        }
      }
    }
    if (newFile !== this.currentFile) {
      this.currentFile = newFile;
      this.updateSyncStatus();
    }
  }
  async updateSyncStatus() {
    if (!this.currentFile) {
      this.syncStatus = {
        title: "unknown",
        slug: "unknown",
        status: "unknown",
        tags: "unknown",
        featured: "unknown",
        created_at: "unknown",
        updated_at: "unknown",
        published_at: "unknown"
      };
      this.render();
      return;
    }
    const articlesPath = path.normalize(this.plugin.settings.articlesDir);
    const filePath = path.normalize(this.currentFile.path);
    if (!filePath.startsWith(articlesPath)) {
      this.syncStatus = {
        title: "unknown",
        slug: "unknown",
        status: "unknown",
        tags: "unknown",
        featured: "unknown",
        created_at: "unknown",
        updated_at: "unknown",
        published_at: "unknown"
      };
      this.render();
      return;
    }
    try {
      const content = await this.app.vault.read(this.currentFile);
      const { frontMatter } = ContentConverter.parseArticle(content);
      const normalizedFrontMatter = ContentConverter.normalizeFrontMatter(frontMatter);
      if (!normalizedFrontMatter.title) {
        this.syncStatus = {
          title: "unknown",
          slug: "unknown",
          status: "unknown",
          tags: "unknown",
          featured: "unknown",
          created_at: "unknown",
          updated_at: "unknown",
          published_at: "unknown"
        };
        this.render();
        return;
      }
      if (!this.plugin.settings.ghostAdminApiKey) {
        this.syncStatus = {
          title: "unknown",
          slug: "unknown",
          status: "unknown",
          tags: "unknown",
          featured: "unknown",
          created_at: "unknown",
          updated_at: "unknown",
          published_at: "unknown"
        };
        this.render();
        return;
      }
      const ghostAPI = new ObsidianGhostAPI(this.plugin.settings.ghostUrl, this.plugin.settings.ghostAdminApiKey);
      const slug = normalizedFrontMatter.slug || ContentConverter.slugify(normalizedFrontMatter.title);
      const ghostPost = await ghostAPI.getPostBySlug(slug);
      if (!ghostPost) {
        this.syncStatus = {
          title: "different",
          slug: "different",
          status: "different",
          tags: "different",
          featured: "different",
          created_at: "different",
          updated_at: "different",
          published_at: "different"
        };
        this.render();
        return;
      }
      this.syncStatus = {
        title: this.compareField(ghostPost.title, normalizedFrontMatter.title),
        slug: this.compareField(ghostPost.slug, slug),
        status: this.compareField(ghostPost.status, normalizedFrontMatter.status || "draft"),
        tags: this.compareTags(ghostPost.tags || [], normalizedFrontMatter.tags || []),
        featured: this.compareField(ghostPost.featured, normalizedFrontMatter.featured || false),
        created_at: this.compareField(ghostPost.created_at, normalizedFrontMatter.created_at || normalizedFrontMatter["Created At"]),
        updated_at: this.compareField(ghostPost.updated_at, normalizedFrontMatter.updated_at || normalizedFrontMatter["Updated At"]),
        published_at: this.compareField(ghostPost.published_at, normalizedFrontMatter.published_at || normalizedFrontMatter["Published At"]),
        ghostPost
      };
    } catch (error) {
      console.error("Error checking sync status:", error);
      this.syncStatus = {
        title: "unknown",
        slug: "unknown",
        status: "unknown",
        tags: "unknown",
        featured: "unknown",
        created_at: "unknown",
        updated_at: "unknown",
        published_at: "unknown"
      };
    }
    this.render();
  }
  compareField(ghostValue, localValue) {
    if (ghostValue === void 0 && localValue === void 0)
      return "synced";
    if (ghostValue === null && localValue === void 0)
      return "synced";
    if (ghostValue === void 0 && localValue === null)
      return "synced";
    if (typeof ghostValue === "string" && typeof localValue === "string") {
      const ghostDate = new Date(ghostValue);
      const localDate = new Date(localValue);
      if (!isNaN(ghostDate.getTime()) && !isNaN(localDate.getTime())) {
        return Math.abs(ghostDate.getTime() - localDate.getTime()) < 1e3 ? "synced" : "different";
      }
    }
    return ghostValue === localValue ? "synced" : "different";
  }
  compareTags(ghostTags, localTags) {
    const ghostTagNames = ghostTags.map((tag) => tag.name).sort();
    const localTagNames = [...localTags].sort();
    if (this.plugin.settings.verbose) {
      console.log("Comparing tags:");
      console.log("Ghost tags:", ghostTagNames);
      console.log("Local tags:", localTagNames);
    }
    if (ghostTagNames.length !== localTagNames.length) {
      if (this.plugin.settings.verbose) {
        console.log("Tag lengths differ:", ghostTagNames.length, "vs", localTagNames.length);
      }
      return "different";
    }
    for (let i = 0; i < ghostTagNames.length; i++) {
      if (ghostTagNames[i] !== localTagNames[i]) {
        if (this.plugin.settings.verbose) {
          console.log("Tag mismatch at index", i, ":", ghostTagNames[i], "vs", localTagNames[i]);
        }
        return "different";
      }
    }
    if (this.plugin.settings.verbose) {
      console.log("Tags match - returning synced");
    }
    return "synced";
  }
  render() {
    const container = this.contentEl;
    container.empty();
    const header = container.createEl("div", { cls: "ghost-sync-header" });
    header.createEl("h3", { text: "Ghost Sync Status" });
    if (!this.currentFile) {
      container.createEl("p", { text: "No file selected", cls: "ghost-sync-no-file" });
      return;
    }
    const articlesPath = path.normalize(this.plugin.settings.articlesDir);
    const filePath = path.normalize(this.currentFile.path);
    if (!filePath.startsWith(articlesPath)) {
      container.createEl("p", {
        text: `File must be in ${this.plugin.settings.articlesDir} directory`,
        cls: "ghost-sync-not-article"
      });
      return;
    }
    const fileInfo = container.createEl("div", { cls: "ghost-sync-file-info" });
    fileInfo.createEl("strong", { text: "Current file:" });
    fileInfo.createEl("div", { text: this.currentFile.name, cls: "ghost-sync-filename" });
    const statusContainer = container.createEl("div", { cls: "ghost-sync-status-container" });
    this.renderStatusItem(statusContainer, "Title", this.syncStatus.title);
    this.renderStatusItem(statusContainer, "Slug", this.syncStatus.slug);
    this.renderStatusItem(statusContainer, "Status", this.syncStatus.status);
    this.renderStatusItem(statusContainer, "Tags", this.syncStatus.tags);
    this.renderStatusItem(statusContainer, "Featured", this.syncStatus.featured);
    this.renderStatusItem(statusContainer, "Created At", this.syncStatus.created_at);
    this.renderStatusItem(statusContainer, "Updated At", this.syncStatus.updated_at);
    this.renderStatusItem(statusContainer, "Published At", this.syncStatus.published_at);
    if (this.syncStatus.ghostPost) {
      this.renderGhostInfo(container, this.syncStatus.ghostPost);
    }
    this.renderControls(container);
  }
  renderStatusItem(container, label, status) {
    const item = container.createEl("div", { cls: "ghost-sync-status-item" });
    item.createEl("span", { text: label + ":", cls: "ghost-sync-status-label" });
    const statusEl = item.createEl("span", { cls: `ghost-sync-status-value ghost-sync-status-${status}` });
    let statusText = "";
    let statusIcon = "";
    switch (status) {
      case "synced":
        statusText = "Synced";
        statusIcon = "\u2713";
        break;
      case "different":
        statusText = "Different";
        statusIcon = "\u26A0";
        break;
      case "unknown":
        statusText = "Unknown";
        statusIcon = "?";
        break;
    }
    statusEl.setText(`${statusIcon} ${statusText}`);
  }
  renderGhostInfo(container, ghostPost) {
    var _a;
    const ghostInfo = container.createEl("div", { cls: "ghost-sync-ghost-info" });
    ghostInfo.createEl("h4", { text: "Ghost Post Info" });
    const infoGrid = ghostInfo.createEl("div", { cls: "ghost-sync-info-grid" });
    this.renderInfoItem(infoGrid, "Status", ghostPost.status);
    this.renderInfoItem(infoGrid, "Featured", ghostPost.featured ? "Yes" : "No");
    this.renderInfoItem(infoGrid, "Tags", ((_a = ghostPost.tags) == null ? void 0 : _a.map((t) => t.name).join(", ")) || "None");
    this.renderInfoItem(infoGrid, "Created", ghostPost.created_at ? new Date(ghostPost.created_at).toLocaleDateString() : "Unknown");
    this.renderInfoItem(infoGrid, "Updated", ghostPost.updated_at ? new Date(ghostPost.updated_at).toLocaleDateString() : "Unknown");
    this.renderInfoItem(infoGrid, "Published", ghostPost.published_at ? new Date(ghostPost.published_at).toLocaleDateString() : "Not published");
  }
  renderInfoItem(container, label, value) {
    const item = container.createEl("div", { cls: "ghost-sync-info-item" });
    item.createEl("span", { text: label + ":", cls: "ghost-sync-info-label" });
    item.createEl("span", { text: value, cls: "ghost-sync-info-value" });
  }
  renderControls(container) {
    const controls = container.createEl("div", { cls: "ghost-sync-controls" });
    const syncToGhostBtn = controls.createEl("button", {
      text: "Sync to Ghost",
      cls: "mod-cta ghost-sync-btn"
    });
    syncToGhostBtn.onclick = async () => {
      await this.plugin.syncCurrentPostToGhost();
      setTimeout(() => this.updateSyncStatus(), 1e3);
    };
    const syncFromGhostBtn = controls.createEl("button", {
      text: "Sync from Ghost",
      cls: "ghost-sync-btn"
    });
    syncFromGhostBtn.onclick = async () => {
      await this.syncFromGhost();
      setTimeout(() => this.updateSyncStatus(), 1e3);
    };
    const refreshBtn = controls.createEl("button", {
      text: "Refresh",
      cls: "ghost-sync-btn"
    });
    refreshBtn.onclick = () => {
      this.updateCurrentFile();
      this.updateSyncStatus();
    };
  }
  async syncFromGhost() {
    if (!this.currentFile || !this.syncStatus.ghostPost) {
      new import_obsidian2.Notice("No Ghost post to sync from");
      return;
    }
    try {
      new import_obsidian2.Notice("Syncing from Ghost...");
      const articleContent = ContentConverter.convertGhostPostToArticle(this.syncStatus.ghostPost);
      await this.app.vault.modify(this.currentFile, articleContent);
      new import_obsidian2.Notice("Synced from Ghost successfully");
      setTimeout(() => this.updateSyncStatus(), 1e3);
    } catch (error) {
      console.error("Error syncing from Ghost:", error);
      new import_obsidian2.Notice(`Error syncing from Ghost: ${error.message}`);
    }
  }
};

// src/settings/settings-tab.ts
var import_obsidian3 = require("obsidian");
var GhostSyncSettingTab = class extends import_obsidian3.PluginSettingTab {
  constructor(app, plugin) {
    super(app, plugin);
    this.plugin = plugin;
  }
  display() {
    const { containerEl } = this;
    containerEl.empty();
    containerEl.createEl("h2", { text: "Ghost Sync Settings" });
    new import_obsidian3.Setting(containerEl).setName("Ghost site URL").setDesc("Your Ghost site URL (e.g., https://your-site.ghost.io)").addText((text) => text.setPlaceholder("https://your-site.ghost.io").setValue(this.plugin.settings.ghostUrl).onChange(async (value) => {
      this.plugin.settings.ghostUrl = value;
      await this.plugin.saveSettings();
    }));
    new import_obsidian3.Setting(containerEl).setName("Ghost Admin API Key").setDesc("Your Ghost Admin API key (format: id:secret)").addText((text) => text.setPlaceholder("id:secret").setValue(this.plugin.settings.ghostAdminApiKey).onChange(async (value) => {
      this.plugin.settings.ghostAdminApiKey = value;
      await this.plugin.saveSettings();
    }));
    new import_obsidian3.Setting(containerEl).setName("Articles directory").setDesc("Directory where your articles are stored").addText((text) => text.setPlaceholder("articles").setValue(this.plugin.settings.articlesDir).onChange(async (value) => {
      this.plugin.settings.articlesDir = value;
      await this.plugin.saveSettings();
    }));
    new import_obsidian3.Setting(containerEl).setName("Verbose output").setDesc("Show detailed output in console").addToggle((toggle) => toggle.setValue(this.plugin.settings.verbose).onChange(async (value) => {
      this.plugin.settings.verbose = value;
      await this.plugin.saveSettings();
    }));
  }
};

// src/main.ts
var TurndownService2 = require_turndown_browser_cjs();
var PostSelectionModal = class extends import_obsidian4.SuggestModal {
  constructor(app, posts, onSelect) {
    super(app);
    this.posts = posts;
    this.onSelect = onSelect;
    this.setPlaceholder("Type to search posts...");
  }
  getSuggestions(query) {
    return this.posts.filter(
      (post) => post.title.toLowerCase().includes(query.toLowerCase()) || post.slug.toLowerCase().includes(query.toLowerCase())
    );
  }
  renderSuggestion(post, el) {
    var _a;
    const container = el.createDiv({ cls: "ghost-post-suggestion" });
    const title = container.createDiv({ cls: "ghost-post-title" });
    title.setText(post.title);
    const meta = container.createDiv({ cls: "ghost-post-meta" });
    const status = post.status === "published" ? "\u{1F4C4}" : "\u{1F4DD}";
    const publishedDate = post.published_at ? new Date(post.published_at).toLocaleDateString() : "Draft";
    const tags = ((_a = post.tags) == null ? void 0 : _a.map((t) => t.name).join(", ")) || "No tags";
    meta.setText(`${status} ${publishedDate} \u2022 ${tags}`);
  }
  onChooseSuggestion(post, evt) {
    this.onSelect(post);
  }
};
var DEFAULT_SETTINGS = {
  ghostUrl: "https://your-site.ghost.io",
  ghostAdminApiKey: "",
  articlesDir: "articles",
  verbose: false
};
var GhostSyncPlugin = class extends import_obsidian4.Plugin {
  async onload() {
    await this.loadSettings();
    this.registerView(
      VIEW_TYPE_GHOST_SYNC_STATUS,
      (leaf) => new GhostSyncStatusView(leaf, this)
    );
    this.addRibbonIcon("upload", "Sync current post to Ghost", () => {
      this.syncCurrentPostToGhost();
    });
    this.addRibbonIcon("sync", "Open Ghost Sync Status", () => {
      this.activateSyncStatusView();
    });
    this.addCommand({
      id: "sync-current-to-ghost",
      name: "Sync current post to Ghost",
      editorCallback: () => {
        this.syncCurrentPostToGhost();
      }
    });
    this.addCommand({
      id: "sync-current-file",
      name: "Sync current file",
      editorCallback: () => {
        this.syncCurrentPostToGhost();
      }
    });
    this.addCommand({
      id: "browse-ghost-posts",
      name: "Browse and sync posts from Ghost",
      callback: () => {
        this.browseGhostPosts();
      }
    });
    this.addCommand({
      id: "sync-from-ghost-by-title",
      name: "Sync post from Ghost by title",
      callback: () => {
        this.syncFromGhostByTitle();
      }
    });
    this.addCommand({
      id: "sync-all-from-ghost",
      name: "Sync all posts from Ghost to local",
      callback: () => {
        this.syncAllFromGhost();
      }
    });
    this.addCommand({
      id: "create-new-post",
      name: "Ghost Sync: Create new post",
      callback: () => {
        this.createNewPost();
      }
    });
    this.addCommand({
      id: "open-sync-status",
      name: "Open Ghost Sync Status",
      callback: () => {
        this.activateSyncStatusView();
      }
    });
    this.addSettingTab(new GhostSyncSettingTab(this.app, this));
  }
  async syncCurrentPostToGhost() {
    const activeView = this.app.workspace.getActiveViewOfType(import_obsidian4.MarkdownView);
    if (!activeView) {
      new import_obsidian4.Notice("No active markdown file");
      return;
    }
    const file = activeView.file;
    if (!file) {
      new import_obsidian4.Notice("No file is currently open");
      return;
    }
    const articlesPath = path2.normalize(this.settings.articlesDir);
    const filePath = path2.normalize(file.path);
    if (!filePath.startsWith(articlesPath)) {
      new import_obsidian4.Notice(`File must be in the ${this.settings.articlesDir} directory to sync to Ghost`);
      return;
    }
    try {
      if (!this.settings.ghostAdminApiKey) {
        new import_obsidian4.Notice("Ghost Admin API key not configured. Please check plugin settings.");
        return;
      }
      const content = await this.app.vault.read(file);
      const { frontMatter, markdownContent } = ContentConverter.parseArticle(content);
      const normalizedFrontMatter = ContentConverter.normalizeFrontMatter(frontMatter);
      if (!normalizedFrontMatter.title) {
        new import_obsidian4.Notice("Could not find title in frontmatter");
        return;
      }
      const title = normalizedFrontMatter.title;
      new import_obsidian4.Notice(`Syncing "${title}" to Ghost...`);
      const ghostAPI = new ObsidianGhostAPI(this.settings.ghostUrl, this.settings.ghostAdminApiKey);
      const slug = normalizedFrontMatter.slug || ContentConverter.slugify(title);
      const existingPost = await ghostAPI.getPostBySlug(slug);
      const isUpdate = !!existingPost;
      const postData = ContentConverter.createGhostPostData(frontMatter, markdownContent, {
        status: "published",
        isUpdate,
        existingPost
      });
      let result;
      if (isUpdate) {
        result = await ghostAPI.updatePost(postData);
        new import_obsidian4.Notice(`Updated "${title}" in Ghost`);
      } else {
        result = await ghostAPI.createPost(postData);
        new import_obsidian4.Notice(`Created "${title}" in Ghost`);
      }
      if (this.settings.verbose) {
        console.log("Ghost sync result:", result);
      }
    } catch (error) {
      console.error("Error syncing to Ghost:", error);
      new import_obsidian4.Notice(`Error syncing to Ghost: ${error.message}`);
    }
  }
  async syncFromGhostByTitle() {
    const title = await this.promptForTitle();
    if (!title)
      return;
    try {
      if (!this.settings.ghostAdminApiKey) {
        new import_obsidian4.Notice("Ghost Admin API key not configured. Please check plugin settings.");
        return;
      }
      new import_obsidian4.Notice(`Syncing "${title}" from Ghost...`);
      const ghostAPI = new ObsidianGhostAPI(this.settings.ghostUrl, this.settings.ghostAdminApiKey);
      const posts = await ghostAPI.getPosts();
      const post = posts.find((p) => p.title.toLowerCase() === title.toLowerCase());
      if (!post) {
        new import_obsidian4.Notice(`Post "${title}" not found in Ghost`);
        return;
      }
      const articleContent = ContentConverter.convertGhostPostToArticle(post);
      const filename = post.slug + ".md";
      const filePath = path2.posix.join(this.settings.articlesDir, filename);
      const dir = path2.dirname(filePath);
      if (dir !== "." && dir !== this.settings.articlesDir) {
        await this.app.vault.createFolder(dir).catch(() => {
        });
      }
      const existingFile = this.app.vault.getAbstractFileByPath(filePath);
      if (existingFile) {
        await this.app.vault.modify(existingFile, articleContent);
        new import_obsidian4.Notice(`Updated "${post.title}" in ${filePath}`);
      } else {
        await this.app.vault.create(filePath, articleContent);
        new import_obsidian4.Notice(`Created "${post.title}" in ${filePath}`);
      }
      if (this.settings.verbose) {
        console.log("Ghost sync result:", { post: post.title, file: filePath });
      }
    } catch (error) {
      console.error("Error syncing from Ghost:", error);
      new import_obsidian4.Notice(`Error syncing from Ghost: ${error.message}`);
    }
  }
  async browseGhostPosts() {
    try {
      if (!this.settings.ghostAdminApiKey) {
        new import_obsidian4.Notice("Ghost Admin API key not configured. Please check plugin settings.");
        return;
      }
      new import_obsidian4.Notice("Fetching posts from Ghost...");
      const ghostAPI = new ObsidianGhostAPI(this.settings.ghostUrl, this.settings.ghostAdminApiKey);
      const posts = await ghostAPI.getPosts();
      if (posts.length === 0) {
        new import_obsidian4.Notice("No posts found in Ghost");
        return;
      }
      const modal = new PostSelectionModal(this.app, posts, async (selectedPost) => {
        try {
          new import_obsidian4.Notice(`Syncing "${selectedPost.title}" from Ghost...`);
          const articleContent = ContentConverter.convertGhostPostToArticle(selectedPost);
          const filename = selectedPost.slug + ".md";
          const filePath = path2.posix.join(this.settings.articlesDir, filename);
          const dir = path2.dirname(filePath);
          if (dir !== "." && dir !== this.settings.articlesDir) {
            await this.app.vault.createFolder(dir).catch(() => {
            });
          }
          const existingFile = this.app.vault.getAbstractFileByPath(filePath);
          if (existingFile) {
            await this.app.vault.modify(existingFile, articleContent);
            new import_obsidian4.Notice(`Updated "${selectedPost.title}" in ${filePath}`);
          } else {
            await this.app.vault.create(filePath, articleContent);
            new import_obsidian4.Notice(`Created "${selectedPost.title}" in ${filePath}`);
          }
          if (this.settings.verbose) {
            console.log("Ghost sync result:", { post: selectedPost.title, file: filePath });
          }
        } catch (error) {
          console.error("Error syncing selected post:", error);
          new import_obsidian4.Notice(`Error syncing "${selectedPost.title}": ${error.message}`);
        }
      });
      modal.open();
    } catch (error) {
      console.error("Error fetching posts from Ghost:", error);
      new import_obsidian4.Notice(`Error fetching posts from Ghost: ${error.message}`);
    }
  }
  async syncAllFromGhost() {
    try {
      if (!this.settings.ghostAdminApiKey) {
        new import_obsidian4.Notice("Ghost Admin API key not configured. Please check plugin settings.");
        return;
      }
      new import_obsidian4.Notice("Syncing all posts from Ghost...");
      const ghostAPI = new ObsidianGhostAPI(this.settings.ghostUrl, this.settings.ghostAdminApiKey);
      const posts = await ghostAPI.getPosts();
      if (posts.length === 0) {
        new import_obsidian4.Notice("No posts found in Ghost");
        return;
      }
      let syncedCount = 0;
      const errors = [];
      for (const post of posts) {
        try {
          const articleContent = ContentConverter.convertGhostPostToArticle(post);
          const filename = post.slug + ".md";
          const filePath = path2.posix.join(this.settings.articlesDir, filename);
          const dir = path2.dirname(filePath);
          if (dir !== "." && dir !== this.settings.articlesDir) {
            await this.app.vault.createFolder(dir).catch(() => {
            });
          }
          const existingFile = this.app.vault.getAbstractFileByPath(filePath);
          if (existingFile) {
            await this.app.vault.modify(existingFile, articleContent);
          } else {
            await this.app.vault.create(filePath, articleContent);
          }
          syncedCount++;
          if (this.settings.verbose) {
            console.log(`Synced: ${post.title} \u2192 ${filePath}`);
          }
        } catch (error) {
          const errorMsg = `Failed to sync "${post.title}": ${error.message}`;
          errors.push(errorMsg);
          console.error(errorMsg, error);
        }
      }
      if (errors.length > 0) {
        new import_obsidian4.Notice(`Synced ${syncedCount} posts with ${errors.length} errors. Check console for details.`);
        console.error("Sync errors:", errors);
      } else {
        new import_obsidian4.Notice(`Successfully synced all ${syncedCount} posts from Ghost`);
      }
      if (this.settings.verbose) {
        console.log(`Sync complete: ${syncedCount} synced, ${errors.length} errors`);
      }
    } catch (error) {
      console.error("Error syncing from Ghost:", error);
      new import_obsidian4.Notice(`Error syncing from Ghost: ${error.message}`);
    }
  }
  async promptForTitle() {
    return new Promise((resolve) => {
      const modal = new TitleInputModal(this.app, (title) => {
        resolve(title);
      });
      modal.open();
    });
  }
  getVaultPath() {
    const adapter = this.app.vault.adapter;
    if (adapter.basePath) {
      return adapter.basePath;
    }
    return ".";
  }
  async createNewPost() {
    const title = await this.promptForTitle();
    if (!title) {
      return;
    }
    try {
      const slug = ContentConverter.slugify(title);
      const filename = slug + ".md";
      const filePath = path2.posix.join(this.settings.articlesDir, filename);
      const existingFile = this.app.vault.getAbstractFileByPath(filePath);
      if (existingFile) {
        new import_obsidian4.Notice(`File "${filename}" already exists in ${this.settings.articlesDir}`);
        return;
      }
      const now = new Date();
      const createdAt = now.toISOString();
      const frontmatter = {};
      frontmatter["Title"] = title;
      frontmatter["Slug"] = slug;
      frontmatter["Status"] = "draft";
      frontmatter["Created At"] = createdAt;
      frontmatter["Updated At"] = createdAt;
      frontmatter["Tags"] = [];
      const yamlFrontmatter = ContentConverter.objectToYaml(frontmatter);
      const articleContent = `---
${yamlFrontmatter}---

# ${title}

Write your content here...
`;
      const articlesDir = this.settings.articlesDir;
      try {
        await this.app.vault.createFolder(articlesDir);
      } catch (error) {
      }
      const file = await this.app.vault.create(filePath, articleContent);
      const leaf = this.app.workspace.getUnpinnedLeaf();
      await leaf.openFile(file);
      new import_obsidian4.Notice(`Created new post: "${title}"`);
      if (this.settings.verbose) {
        console.log(`Created new post: ${title} \u2192 ${filePath}`);
      }
    } catch (error) {
      console.error("Error creating new post:", error);
      new import_obsidian4.Notice(`Error creating new post: ${error.message}`);
    }
  }
  async activateSyncStatusView() {
    const { workspace } = this.app;
    let leaf = null;
    const leaves = workspace.getLeavesOfType(VIEW_TYPE_GHOST_SYNC_STATUS);
    if (leaves.length > 0) {
      leaf = leaves[0];
    } else {
      leaf = workspace.getRightLeaf(false);
      await leaf.setViewState({ type: VIEW_TYPE_GHOST_SYNC_STATUS, active: true });
    }
    workspace.revealLeaf(leaf);
  }
  async loadSettings() {
    this.settings = Object.assign({}, DEFAULT_SETTINGS, await this.loadData());
  }
  async saveSettings() {
    await this.saveData(this.settings);
  }
};
var TitleInputModal = class extends import_obsidian4.Modal {
  constructor(app, onSubmit) {
    super(app);
    this.onSubmit = onSubmit;
  }
  onOpen() {
    const { contentEl } = this;
    contentEl.createEl("h2", { text: "Enter post title" });
    const inputEl = contentEl.createEl("input", {
      type: "text",
      placeholder: "Post title..."
    });
    inputEl.focus();
    const buttonEl = contentEl.createEl("button", {
      text: "Create"
    });
    const handleSubmit = () => {
      const title = inputEl.value.trim();
      if (title) {
        this.close();
        this.onSubmit(title);
      }
    };
    buttonEl.onclick = handleSubmit;
    inputEl.onkeydown = (e) => {
      if (e.key === "Enter") {
        handleSubmit();
      }
    };
  }
  onClose() {
    const { contentEl } = this;
    contentEl.empty();
  }
};
