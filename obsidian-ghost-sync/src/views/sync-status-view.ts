import { ItemView, <PERSON>pace<PERSON>ea<PERSON>, <PERSON><PERSON><PERSON>ie<PERSON>, TF<PERSON>, Notice } from "obsidian";
import * as path from "path";
import { ContentConverter } from "../utils/content-converter";
import { ObsidianGhostAPI } from "../api/ghost-api";
import { SyncStatusData, SyncStatus, GhostPost, ArticleFrontMatter } from "../types";
import type GhostSyncPlugin from "../main";

export const VIEW_TYPE_GHOST_SYNC_STATUS = 'ghost-sync-status';

export class GhostSyncStatusView extends ItemView {
  plugin: GhostSyncPlugin;
  private currentFile: TFile | null = null;
  private syncStatus: SyncStatusData = {
    title: 'unknown',
    slug: 'unknown',
    status: 'unknown',
    tags: 'unknown',
    featured: 'unknown',
    created_at: 'unknown',
    updated_at: 'unknown',
    published_at: 'unknown'
  };

  constructor(leaf: WorkspaceLeaf, plugin: GhostSyncPlugin) {
    super(leaf);
    this.plugin = plugin;
  }

  getViewType() {
    return VIEW_TYPE_GHOST_SYNC_STATUS;
  }

  getDisplayText() {
    return 'Ghost Sync Status';
  }

  getIcon() {
    return 'sync';
  }

  async onOpen() {
    const container = this.contentEl;
    container.empty();
    container.addClass('ghost-sync-status-view');

    // Listen for active file changes
    this.registerEvent(
      this.app.workspace.on('active-leaf-change', () => {
        this.updateCurrentFile();
      })
    );

    // Listen for file open events
    this.registerEvent(
      this.app.workspace.on('file-open', () => {
        this.updateCurrentFile();
      })
    );

    // Listen for file changes
    this.registerEvent(
      this.app.vault.on('modify', (file) => {
        if (file === this.currentFile) {
          this.updateSyncStatus();
        }
      })
    );

    this.updateCurrentFile();
    this.render();
  }

  async onClose() {
    // Cleanup is handled automatically by registerEvent
  }

  private updateCurrentFile() {
    // Try multiple ways to get the current file to be more robust
    let newFile: TFile | null = null;

    // First try to get from active editor (most reliable)
    const activeEditor = this.app.workspace.activeEditor;
    if (activeEditor?.file) {
      newFile = activeEditor.file;
    } else {
      // Fallback to active markdown view
      const activeView = this.app.workspace.getActiveViewOfType(MarkdownView);
      if (activeView?.file) {
        newFile = activeView.file;
      } else {
        // Last resort: check all markdown views for the most recently active one
        const markdownLeaves = this.app.workspace.getLeavesOfType('markdown');
        for (const leaf of markdownLeaves) {
          if (leaf.view instanceof MarkdownView && leaf.view.file) {
            newFile = leaf.view.file;
            break;
          }
        }
      }
    }

    if (newFile !== this.currentFile) {
      this.currentFile = newFile;
      this.updateSyncStatus();
    }
  }

  private async updateSyncStatus() {
    if (!this.currentFile) {
      this.syncStatus = {
        title: 'unknown',
        slug: 'unknown',
        status: 'unknown',
        tags: 'unknown',
        featured: 'unknown',
        created_at: 'unknown',
        updated_at: 'unknown',
        published_at: 'unknown'
      };
      this.render();
      return;
    }

    // Check if file is in articles directory
    const articlesPath = path.normalize(this.plugin.settings.articlesDir);
    const filePath = path.normalize(this.currentFile.path);

    if (!filePath.startsWith(articlesPath)) {
      this.syncStatus = {
        title: 'unknown',
        slug: 'unknown',
        status: 'unknown',
        tags: 'unknown',
        featured: 'unknown',
        created_at: 'unknown',
        updated_at: 'unknown',
        published_at: 'unknown'
      };
      this.render();
      return;
    }

    try {
      // Get local content
      const content = await this.app.vault.read(this.currentFile);
      const { frontMatter } = ContentConverter.parseArticle(content);
      const normalizedFrontMatter = ContentConverter.normalizeFrontMatter(frontMatter);

      if (!normalizedFrontMatter.title) {
        this.syncStatus = {
          title: 'unknown',
          slug: 'unknown',
          status: 'unknown',
          tags: 'unknown',
          featured: 'unknown',
          created_at: 'unknown',
          updated_at: 'unknown',
          published_at: 'unknown'
        };
        this.render();
        return;
      }

      // Get Ghost post
      if (!this.plugin.settings.ghostAdminApiKey) {
        this.syncStatus = {
          title: 'unknown',
          slug: 'unknown',
          status: 'unknown',
          tags: 'unknown',
          featured: 'unknown',
          created_at: 'unknown',
          updated_at: 'unknown',
          published_at: 'unknown'
        };
        this.render();
        return;
      }

      const ghostAPI = new ObsidianGhostAPI(this.plugin.settings.ghostUrl, this.plugin.settings.ghostAdminApiKey);
      const slug = normalizedFrontMatter.slug || ContentConverter.slugify(normalizedFrontMatter.title);
      const ghostPost = await ghostAPI.getPostBySlug(slug);

      if (!ghostPost) {
        this.syncStatus = {
          title: 'different',
          slug: 'different',
          status: 'different',
          tags: 'different',
          featured: 'different',
          created_at: 'different',
          updated_at: 'different',
          published_at: 'different'
        };
        this.render();
        return;
      }

      // Compare all fields
      this.syncStatus = {
        title: this.compareField(ghostPost.title, normalizedFrontMatter.title),
        slug: this.compareField(ghostPost.slug, slug),
        status: this.compareField(ghostPost.status, normalizedFrontMatter.status || 'draft'),
        tags: this.compareTags(ghostPost.tags || [], normalizedFrontMatter.tags || []),
        featured: this.compareField(ghostPost.featured, normalizedFrontMatter.featured || false),
        created_at: this.compareField(ghostPost.created_at, normalizedFrontMatter.created_at || normalizedFrontMatter['Created At']),
        updated_at: this.compareField(ghostPost.updated_at, normalizedFrontMatter.updated_at || normalizedFrontMatter['Updated At']),
        published_at: this.compareField(ghostPost.published_at, normalizedFrontMatter.published_at || normalizedFrontMatter['Published At']),
        ghostPost: ghostPost
      };

    } catch (error) {
      console.error('Error checking sync status:', error);
      this.syncStatus = {
        title: 'unknown',
        slug: 'unknown',
        status: 'unknown',
        tags: 'unknown',
        featured: 'unknown',
        created_at: 'unknown',
        updated_at: 'unknown',
        published_at: 'unknown'
      };
    }

    this.render();
  }

  private compareField(ghostValue: any, localValue: any): SyncStatus {
    if (ghostValue === undefined && localValue === undefined) return 'synced';
    if (ghostValue === null && localValue === undefined) return 'synced';
    if (ghostValue === undefined && localValue === null) return 'synced';

    // Handle date comparison
    if (typeof ghostValue === 'string' && typeof localValue === 'string') {
      // Try to parse as dates
      const ghostDate = new Date(ghostValue);
      const localDate = new Date(localValue);
      if (!isNaN(ghostDate.getTime()) && !isNaN(localDate.getTime())) {
        return Math.abs(ghostDate.getTime() - localDate.getTime()) < 1000 ? 'synced' : 'different';
      }
    }

    return ghostValue === localValue ? 'synced' : 'different';
  }

  private compareTags(ghostTags: any[], localTags: string[]): SyncStatus {
    // Preserve order - don't sort tags as order matters for primary tag
    const ghostTagNames = ghostTags.map(tag => tag.name);
    const localTagNames = [...localTags]; // Create a copy without sorting

    // Debug logging
    if (this.plugin.settings.verbose) {
      console.log('Comparing tags (order preserved):');
      console.log('Ghost tags:', ghostTagNames);
      console.log('Local tags:', localTagNames);
    }

    if (ghostTagNames.length !== localTagNames.length) {
      if (this.plugin.settings.verbose) {
        console.log('Tag lengths differ:', ghostTagNames.length, 'vs', localTagNames.length);
      }
      return 'different';
    }

    for (let i = 0; i < ghostTagNames.length; i++) {
      if (ghostTagNames[i] !== localTagNames[i]) {
        if (this.plugin.settings.verbose) {
          console.log('Tag mismatch at index', i, ':', ghostTagNames[i], 'vs', localTagNames[i]);
        }
        return 'different';
      }
    }

    if (this.plugin.settings.verbose) {
      console.log('Tags match - returning synced');
    }
    return 'synced';
  }

  private render() {
    const container = this.contentEl;
    container.empty();

    // Header
    const header = container.createEl('div', { cls: 'ghost-sync-header' });
    header.createEl('h3', { text: 'Ghost Sync Status' });

    if (!this.currentFile) {
      container.createEl('p', { text: 'No file selected', cls: 'ghost-sync-no-file' });
      return;
    }

    // Check if file is in articles directory
    const articlesPath = path.normalize(this.plugin.settings.articlesDir);
    const filePath = path.normalize(this.currentFile.path);

    if (!filePath.startsWith(articlesPath)) {
      container.createEl('p', {
        text: `File must be in ${this.plugin.settings.articlesDir} directory`,
        cls: 'ghost-sync-not-article'
      });
      return;
    }

    // Current file info
    const fileInfo = container.createEl('div', { cls: 'ghost-sync-file-info' });
    fileInfo.createEl('strong', { text: 'Current file:' });
    fileInfo.createEl('div', { text: this.currentFile.name, cls: 'ghost-sync-filename' });

    // Sync status
    const statusContainer = container.createEl('div', { cls: 'ghost-sync-status-container' });

    this.renderStatusItem(statusContainer, 'Title', this.syncStatus.title);
    this.renderStatusItem(statusContainer, 'Slug', this.syncStatus.slug);
    this.renderStatusItem(statusContainer, 'Status', this.syncStatus.status);
    this.renderStatusItem(statusContainer, 'Tags', this.syncStatus.tags);
    this.renderStatusItem(statusContainer, 'Featured', this.syncStatus.featured);
    this.renderStatusItem(statusContainer, 'Created At', this.syncStatus.created_at);
    this.renderStatusItem(statusContainer, 'Updated At', this.syncStatus.updated_at);
    this.renderStatusItem(statusContainer, 'Published At', this.syncStatus.published_at);

    // Ghost post info
    if (this.syncStatus.ghostPost) {
      this.renderGhostInfo(container, this.syncStatus.ghostPost);
    }

    // Sync controls
    this.renderControls(container);
  }

  private renderStatusItem(container: HTMLElement, label: string, status: SyncStatus) {
    const item = container.createEl('div', { cls: 'ghost-sync-status-item' });

    item.createEl('span', { text: label + ':', cls: 'ghost-sync-status-label' });

    const statusEl = item.createEl('span', { cls: `ghost-sync-status-value ghost-sync-status-${status}` });

    let statusText = '';
    let statusIcon = '';

    switch (status) {
      case 'synced':
        statusText = 'Synced';
        statusIcon = '✓';
        break;
      case 'different':
        statusText = 'Different';
        statusIcon = '⚠';
        break;
      case 'unknown':
        statusText = 'Unknown';
        statusIcon = '?';
        break;
    }

    statusEl.setText(`${statusIcon} ${statusText}`);
  }

  private renderGhostInfo(container: HTMLElement, ghostPost: GhostPost) {
    const ghostInfo = container.createEl('div', { cls: 'ghost-sync-ghost-info' });
    ghostInfo.createEl('h4', { text: 'Ghost Post Info' });

    const infoGrid = ghostInfo.createEl('div', { cls: 'ghost-sync-info-grid' });

    this.renderInfoItem(infoGrid, 'Status', ghostPost.status);
    this.renderInfoItem(infoGrid, 'Featured', ghostPost.featured ? 'Yes' : 'No');
    this.renderInfoItem(infoGrid, 'Tags', ghostPost.tags?.map(t => t.name).join(', ') || 'None');
    this.renderInfoItem(infoGrid, 'Created', ghostPost.created_at ?
      new Date(ghostPost.created_at).toLocaleDateString() : 'Unknown');
    this.renderInfoItem(infoGrid, 'Updated', ghostPost.updated_at ?
      new Date(ghostPost.updated_at).toLocaleDateString() : 'Unknown');
    this.renderInfoItem(infoGrid, 'Published', ghostPost.published_at ?
      new Date(ghostPost.published_at).toLocaleDateString() : 'Not published');
  }

  private renderInfoItem(container: HTMLElement, label: string, value: string) {
    const item = container.createEl('div', { cls: 'ghost-sync-info-item' });
    item.createEl('span', { text: label + ':', cls: 'ghost-sync-info-label' });
    item.createEl('span', { text: value, cls: 'ghost-sync-info-value' });
  }

  private renderControls(container: HTMLElement) {
    const controls = container.createEl('div', { cls: 'ghost-sync-controls' });

    const syncToGhostBtn = controls.createEl('button', {
      text: 'Sync to Ghost',
      cls: 'mod-cta ghost-sync-btn'
    });
    syncToGhostBtn.onclick = async () => {
      await this.plugin.syncCurrentPostToGhost();
      // Refresh status after sync
      setTimeout(() => this.updateSyncStatus(), 1000);
    };

    const syncFromGhostBtn = controls.createEl('button', {
      text: 'Sync from Ghost',
      cls: 'ghost-sync-btn'
    });
    syncFromGhostBtn.onclick = async () => {
      await this.syncFromGhost();
      // Refresh status after sync
      setTimeout(() => this.updateSyncStatus(), 1000);
    };

    const refreshBtn = controls.createEl('button', {
      text: 'Refresh',
      cls: 'ghost-sync-btn'
    });
    refreshBtn.onclick = () => {
      this.updateCurrentFile();
      this.updateSyncStatus();
    };
  }

  private async syncFromGhost() {
    if (!this.currentFile || !this.syncStatus.ghostPost) {
      new Notice('No Ghost post to sync from');
      return;
    }

    try {
      new Notice('Syncing from Ghost...');

      // Convert Ghost post to article format
      const articleContent = ContentConverter.convertGhostPostToArticle(this.syncStatus.ghostPost);

      // Update the current file
      await this.app.vault.modify(this.currentFile, articleContent);

      new Notice('Synced from Ghost successfully');

      // Refresh status with a longer delay to ensure file is written
      setTimeout(() => this.updateSyncStatus(), 1000);

    } catch (error) {
      console.error('Error syncing from Ghost:', error);
      new Notice(`Error syncing from Ghost: ${error.message}`);
    }
  }
}
