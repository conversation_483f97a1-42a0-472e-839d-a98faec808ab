import { requestUrl } from "obsidian";
import { GhostPost } from "../types";

export class ObsidianGhostAPI {
	private url: string;
	private key: string;

	constructor(url: string, key: string) {
		this.url = url.replace(/\/$/, ''); // Remove trailing slash
		this.key = key;
	}

	private async request(endpoint: string, options: any = {}): Promise<any> {
		const [id, secret] = this.key.split(':');

		if (!id || !secret) {
			throw new Error('Invalid Ghost Admin API key format. Expected format: id:secret');
		}

		// Create JWT token
		const header = {
			alg: 'HS256',
			typ: 'JWT',
			kid: id
		};

		const now = Math.floor(Date.now() / 1000);
		const payload = {
			iat: now,
			exp: now + 300, // 5 minutes
			aud: '/admin/'
		};

		const token = await this.createJWT(header, payload, secret);

		const url = `${this.url}/ghost/api/admin/${endpoint}`;

		const requestOptions: any = {
			url,
			method: options.method || 'GET',
			headers: {
				'Authorization': `Ghost ${token}`,
				'Content-Type': 'application/json',
				'Accept-Version': 'v5.0'
			}
		};

		if (options.body) {
			requestOptions.body = JSON.stringify(options.body);
		}

		const response = await requestUrl(requestOptions);

		if (response.status >= 400) {
			throw new Error(`Ghost API error: ${response.status} ${response.text}`);
		}

		return response.json;
	}

	private async createJWT(header: any, payload: any, secret: string): Promise<string> {
		const encoder = new TextEncoder();

		const headerB64 = btoa(JSON.stringify(header)).replace(/=/g, '').replace(/\+/g, '-').replace(/\//g, '_');
		const payloadB64 = btoa(JSON.stringify(payload)).replace(/=/g, '').replace(/\+/g, '-').replace(/\//g, '_');

		const data = `${headerB64}.${payloadB64}`;

		// Convert hex secret to ArrayBuffer (Ghost Admin API keys are hex-encoded)
		const secretBuffer = this.hexToArrayBuffer(secret);

		const key = await crypto.subtle.importKey(
			'raw',
			secretBuffer,
			{ name: 'HMAC', hash: 'SHA-256' },
			false,
			['sign']
		);

		const signature = await crypto.subtle.sign('HMAC', key, encoder.encode(data));
		const signatureB64 = btoa(String.fromCharCode(...new Uint8Array(signature)))
			.replace(/=/g, '').replace(/\+/g, '-').replace(/\//g, '_');

		return `${data}.${signatureB64}`;
	}

	private hexToArrayBuffer(hex: string): ArrayBuffer {
		const bytes = new Uint8Array(hex.length / 2);
		for (let i = 0; i < hex.length; i += 2) {
			bytes[i / 2] = parseInt(hex.substring(i, i + 2), 16);
		}
		return bytes.buffer;
	}

	async getPosts(options: any = {}): Promise<GhostPost[]> {
		const params = new URLSearchParams({
			limit: options.limit?.toString() || '100',
			page: options.page?.toString() || '1',
			include: options.include || 'tags,authors',
			formats: options.formats || 'html,lexical'
		});

		const response = await this.request(`posts/?${params}`);
		return response.posts || [];
	}

	async getPostBySlug(slug: string): Promise<GhostPost | null> {
		const response = await this.request(`posts/slug/${slug}/?include=tags,authors&formats=html,lexical`);
		return response.posts?.[0] || null;
	}

	async createPost(postData: any): Promise<GhostPost> {
		const response = await this.request('posts/', {
			method: 'POST',
			body: { posts: [postData] }
		});
		return response.posts?.[0];
	}

	async updatePost(postData: any): Promise<GhostPost> {
		const response = await this.request(`posts/${postData.id}/`, {
			method: 'PUT',
			body: { posts: [postData] }
		});
		return response.posts?.[0];
	}
}
