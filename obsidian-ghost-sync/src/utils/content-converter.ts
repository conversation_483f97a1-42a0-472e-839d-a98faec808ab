// Import HTML to Markdown converter
const TurndownService = require('turndown');

// Content conversion utilities
export class ContentConverter {
  static htmlToMarkdown(html: string): string {
    if (!html) return '';

    // Use Turndown for proper HTML to Markdown conversion
    const turndownService = new TurndownService({
      headingStyle: 'atx',
      codeBlockStyle: 'fenced',
      fence: '```',
      emDelimiter: '*',
      strongDelimiter: '**',
      linkStyle: 'inlined',
      linkReferenceStyle: 'full'
    });

    // Add custom rule for code blocks with language detection
    turndownService.addRule('codeBlock', {
      filter: function (node: any) {
        return node.nodeName === 'PRE' && node.firstChild && node.firstChild.nodeName === 'CODE';
      },
      replacement: function (content: string, node: any) {
        const codeElement = node.firstChild;
        const className = codeElement.className || '';
        const languageMatch = className.match(/language-(\w+)/);
        const language = languageMatch ? languageMatch[1] : '';

        // Clean up the content by removing extra whitespace
        const cleanContent = content.trim();

        return '\n\n```' + language + '\n' + cleanContent + '\n```\n\n';
      }
    });

    return turndownService.turndown(html);
  }

  static createFilename(title: string): string {
    return title
      .toLowerCase()
      .replace(/[^\w\s-]/g, '')
      .replace(/\s+/g, '-')
      .replace(/-+/g, '-')
      .trim();
  }

  static slugify(text: string): string {
    return text
      .toLowerCase()
      .replace(/[^\w\s-]/g, '')
      .replace(/\s+/g, '-')
      .replace(/-+/g, '-')
      .trim();
  }

  static normalizeFrontMatter(frontMatter: any): any {
    // Map title-cased properties to lowercase equivalents for Ghost API
    const propertyMap: { [key: string]: string } = {
      "Title": "title",
      "Slug": "slug",
      "Status": "status",
      "Created At": "created_at",
      "Updated At": "updated_at",
      "Published At": "published_at",
      "Date": "published_at", // Map legacy "Date" to "Published At"
      "Tags": "tags",
      "Primary Tag": "primary_tag",
      "Featured Image": "feature_image",
      "Featured": "featured",
      "Visibility": "visibility"
    };

    const normalized: any = {};

    // Copy all properties, mapping title-cased ones to lowercase
    for (const [key, value] of Object.entries(frontMatter)) {
      const mappedKey = propertyMap[key] || key.toLowerCase();

      // Handle legacy "date" property (lowercase) - map to published_at
      if (key.toLowerCase() === 'date') {
        normalized['published_at'] = value;
      } else {
        normalized[mappedKey] = value;
      }
    }

    return normalized;
  }

  static createGhostPostData(frontMatter: any, markdownContent: string, options: any = {}): any {
    const { status = 'draft', isUpdate = false, existingPost = null } = options;

    // Map title-cased properties to lowercase for backward compatibility
    const normalizedFrontMatter = this.normalizeFrontMatter(frontMatter);

    const slug = normalizedFrontMatter.slug || this.slugify(normalizedFrontMatter.title);
    const newStatus = normalizedFrontMatter.status || status;

    const postData: any = {
      title: normalizedFrontMatter.title,
      slug: slug,
      feature_image: normalizedFrontMatter.feature_image || normalizedFrontMatter.image || null,
      featured: normalizedFrontMatter.featured || false,
      status: newStatus,
      visibility: normalizedFrontMatter.visibility || 'public',
      custom_excerpt: this.generateExcerpt(normalizedFrontMatter, markdownContent)
    };

    // Handle tags with primary tag support
    if (normalizedFrontMatter.tags && Array.isArray(normalizedFrontMatter.tags)) {
      let tags = [...normalizedFrontMatter.tags];

      // If primary tag is specified, ensure it's first in the array
      if (normalizedFrontMatter.primary_tag && typeof normalizedFrontMatter.primary_tag === 'string') {
        // Remove primary tag from current position if it exists
        tags = tags.filter(tag => tag !== normalizedFrontMatter.primary_tag);
        // Add primary tag at the beginning
        tags.unshift(normalizedFrontMatter.primary_tag);
      }

      postData.tags = tags;
    }

    // Handle published_at date carefully
    if (isUpdate && existingPost) {
      // When updating an existing post
      const existingStatus = existingPost.status;
      const existingPublishedAt = existingPost.published_at;

      if (existingStatus === 'draft' && newStatus === 'published') {
        // Draft → Published transition: Set published_at to now
        postData.published_at = new Date().toISOString();
      } else if (existingPublishedAt) {
        // Post was already published: Preserve existing published_at date
        postData.published_at = existingPublishedAt;
      } else if (newStatus === 'published') {
        // Edge case: Post has no published_at but status is published
        postData.published_at = new Date().toISOString();
      }
      // For draft posts, don't set published_at (Ghost will handle it)
    } else {
      // Creating new post
      if (newStatus === 'published') {
        // New published post: Use frontmatter date or current time
        const postDate = this.parseDate(normalizedFrontMatter.published_at) ||
                        this.parseDate(normalizedFrontMatter.date) ||
                        new Date();
        postData.published_at = postDate.toISOString();
      }
      // For new draft posts, don't set published_at
    }

    // CRITICAL FIX: Generate HTML content and handle lexical properly
    const htmlContent = this.markdownToHtml(markdownContent);

    // Ensure we never send empty HTML content
    if (!htmlContent || htmlContent.trim() === '' || htmlContent.trim() === '<p></p>') {
      // Provide minimal HTML content to prevent clearing
      postData.html = '<p>Content is being updated...</p>';
    } else {
      postData.html = htmlContent;
    }

    // CRITICAL FIX: Handle lexical content properly
    if (isUpdate) {
      // When updating, DO NOT set lexical to null - this preserves existing lexical content
      // Only set HTML which Ghost can convert to lexical if needed
      // Do not include lexical field at all to preserve existing content
    } else {
      // For new posts, we can set lexical to null since there's no existing content
      postData.lexical = null;
    }

    // Always set mobiledoc to null (deprecated format)
    postData.mobiledoc = null;

    return postData;
  }

  static parseDate(dateStr: string): Date | null {
    if (!dateStr) return null;
    const date = new Date(dateStr);
    return isNaN(date.getTime()) ? null : date;
  }

  static generateExcerpt(frontMatter: any, content: string): string | null {
    // Always generate excerpt from content (no frontmatter excerpt support)
    if (content) {
      // Get plain text from markdown
      const plaintext = content.replace(/[#*`_\[\]()]/g, '').trim();

      if (plaintext.length <= 300) {
        return plaintext;
      }

      // Truncate at word boundary
      const truncated = plaintext.substring(0, 297);
      const lastSpace = truncated.lastIndexOf(' ');

      if (lastSpace > 250) {
        return truncated.substring(0, lastSpace) + '...';
      }

      return truncated + '...';
    }

    return null;
  }

  static markdownToHtml(markdown: string): string {
    // Handle empty or whitespace-only content
    if (!markdown || markdown.trim() === '') {
      return '<p></p>';
    }

    // Use a proper markdown parser to avoid content loss
    // This is a basic but safe implementation that preserves content structure

    // First, handle code blocks to protect them from other transformations
    const codeBlocks: string[] = [];
    let html = markdown.replace(/```(\w+)?\n([\s\S]*?)```/g, (_match, lang, code) => {
      const placeholder = `__CODE_BLOCK_${codeBlocks.length}__`;
      const langClass = lang ? ` class="language-${lang}"` : '';
      codeBlocks.push(`<pre><code${langClass}>${this.escapeHtml(code.trim())}</code></pre>`);
      return placeholder;
    });

    // Handle inline code
    html = html.replace(/`([^`]+)`/g, '<code>$1</code>');

    // Handle headers
    html = html.replace(/^### (.*$)/gm, '<h3>$1</h3>');
    html = html.replace(/^## (.*$)/gm, '<h2>$1</h2>');
    html = html.replace(/^# (.*$)/gm, '<h1>$1</h1>');

    // Handle bold and italic
    html = html.replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>');
    html = html.replace(/\*(.*?)\*/g, '<em>$1</em>');

    // Handle links
    html = html.replace(/\[([^\]]+)\]\(([^)]+)\)/g, '<a href="$2">$1</a>');

    // Handle line breaks (convert double newlines to paragraphs, single to br)
    html = html.replace(/\n\n/g, '</p><p>');
    html = html.replace(/\n/g, '<br>');

    // Wrap in paragraphs if not empty
    if (html.trim()) {
      html = `<p>${html}</p>`;
    } else {
      html = '<p></p>';
    }

    // Clean up empty paragraphs
    html = html.replace(/<p><\/p>/g, '');
    html = html.replace(/<p><br><\/p>/g, '');

    // Restore code blocks
    codeBlocks.forEach((block, index) => {
      html = html.replace(`__CODE_BLOCK_${index}__`, block);
    });

    // Ensure we always return valid HTML
    return html || '<p></p>';
  }

  static escapeHtml(text: string): string {
    const div = document.createElement('div');
    div.textContent = text;
    return div.innerHTML;
  }

  static parseArticle(content: string): { frontMatter: any, markdownContent: string } {
    const frontMatterMatch = content.match(/^---\n([\s\S]*?)\n---\n([\s\S]*)$/);
    if (!frontMatterMatch) {
      throw new Error('No front matter found in article');
    }

    const frontMatter = this.parseYaml(frontMatterMatch[1]);
    const markdownContent = frontMatterMatch[2].trim();

    return { frontMatter, markdownContent };
  }

  static parseYaml(yamlString: string): any {
    const result: any = {};
    const lines = yamlString.split('\n');

    for (const line of lines) {
      const trimmed = line.trim();
      if (!trimmed || trimmed.startsWith('#')) continue;

      if (trimmed.includes(':')) {
        const [key, ...valueParts] = trimmed.split(':');
        const value = valueParts.join(':').trim();

        if (value.startsWith('"') && value.endsWith('"')) {
          result[key.trim()] = value.slice(1, -1);
        } else if (value.startsWith('[') && value.endsWith(']')) {
          result[key.trim()] = value.slice(1, -1).split(',').map(s => s.trim());
        } else if (value === 'true') {
          result[key.trim()] = true;
        } else if (value === 'false') {
          result[key.trim()] = false;
        } else if (!isNaN(Number(value))) {
          result[key.trim()] = Number(value);
        } else {
          result[key.trim()] = value;
        }
      }
    }

    return result;
  }

  static objectToYaml(obj: any): string {
    let yaml = '';
    for (const [key, value] of Object.entries(obj)) {
      if (Array.isArray(value)) {
        yaml += `${key}:\n`;
        for (const item of value) {
          yaml += `  - ${item}\n`;
        }
      } else if (typeof value === 'string') {
        yaml += `${key}: "${value}"\n`;
      } else {
        yaml += `${key}: ${value}\n`;
      }
    }
    return yaml;
  }

  static convertGhostPostToArticle(post: any): string {
    const tags = post.tags ? post.tags.map((tag: any) => tag.name) : [];
    const createdDate = post.created_at ? new Date(post.created_at) : new Date();
    const updatedDate = post.updated_at ? new Date(post.updated_at) : new Date();
    const publishedDate = post.published_at ? new Date(post.published_at) : null;

    // Create frontmatter in the specified order
    const frontmatter: any = {};

    // 1. Title
    frontmatter["Title"] = post.title;

    // 2. Slug
    frontmatter["Slug"] = post.slug;

    // 3. Status
    frontmatter["Status"] = post.status || 'draft';

    // 4. Published At (only if published)
    if (publishedDate) {
      frontmatter["Published At"] = publishedDate.toISOString();
    }

    // 5. Created At
    frontmatter["Created At"] = createdDate.toISOString();

    // 6. Updated At
    frontmatter["Updated At"] = updatedDate.toISOString();

    // 7. Tags
    frontmatter["Tags"] = tags;

    // 7a. Primary Tag (always show for clarity)
    frontmatter["Primary Tag"] = post.primary_tag?.name || null;

    // 8. Visibility (always show for clarity)
    frontmatter["Visibility"] = post.visibility || 'public';

    // 9. Featured Image (only if exists)
    if (post.feature_image) {
      frontmatter["Featured Image"] = post.feature_image;
    }

    // 10. Featured (only if true)
    if (post.featured) {
      frontmatter["Featured"] = post.featured;
    }

    let content = '';

    // MODERN GHOST → LOCAL CONVERSION: Extract markdown from lexical OR convert HTML
    if (post.lexical) {
      try {
        console.log(`✅ LEXICAL PROCESSING: Extracting content for "${post.title}"`);
        const lexicalDoc = JSON.parse(post.lexical);

        // Look for markdown cards in lexical document
        const markdownCard = this.extractMarkdownFromLexical(lexicalDoc);
        if (markdownCard) {
          console.log(`✅ MARKDOWN EXTRACTED: Found markdown card in lexical`);
          content = markdownCard;
        } else {
          console.log(`⚠️ NO MARKDOWN CARD: Converting HTML to markdown`);
          content = this.htmlToMarkdown(post.html);
        }
      } catch (error) {
        console.warn(`⚠️ LEXICAL ERROR: Failed to process lexical for "${post.title}", using HTML`);
        content = this.htmlToMarkdown(post.html);
      }
    } else if (post.html) {
      // Direct HTML → Markdown conversion
      console.log(`✅ HTML → Markdown conversion for "${post.title}"`);
      content = this.htmlToMarkdown(post.html);
    } else {
      console.error(`❌ NO CONTENT: Post "${post.title}" has no lexical or HTML content`);
      content = '';
    }

    const yamlFrontmatter = this.objectToYaml(frontmatter);
    return `---\n${yamlFrontmatter}---\n\n${content}`;
  }

  static extractMarkdownFromLexical(lexicalDoc: any): string | null {
    // Extract markdown content from lexical document structure
    try {
      if (lexicalDoc?.root?.children) {
        for (const child of lexicalDoc.root.children) {
          // Look for markdown cards
          if (child.type === 'markdown' && child.markdown) {
            return child.markdown;
          }
        }
      }
      return null;
    } catch (error) {
      console.warn('Failed to extract markdown from lexical:', error);
      return null;
    }
  }
}
