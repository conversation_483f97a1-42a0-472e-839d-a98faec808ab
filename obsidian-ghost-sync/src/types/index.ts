export interface GhostSyncSettings {
	ghostUrl: string;
	ghostAdminApiKey: string;
	articlesDir: string;
	verbose: boolean;
}

export interface GhostPost {
	id?: string;
	title: string;
	slug: string;
	status: 'draft' | 'published' | 'scheduled';
	html?: string;
	lexical?: string;
	tags?: GhostTag[];
	featured: boolean;
	created_at: string;
	updated_at: string;
	published_at?: string;
	excerpt?: string;
	feature_image?: string;
	authors?: GhostAuthor[];
}

export interface GhostTag {
	id?: string;
	name: string;
	slug: string;
}

export interface GhostAuthor {
	id?: string;
	name: string;
	slug: string;
	email?: string;
}

export interface ArticleFrontMatter {
	title?: string;
	Title?: string;
	slug?: string;
	Slug?: string;
	status?: 'draft' | 'published' | 'scheduled';
	Status?: 'draft' | 'published' | 'scheduled';
	tags?: string[];
	Tags?: string[];
	featured?: boolean;
	Featured?: boolean;
	'Created At'?: string;
	'created_at'?: string;
	'Updated At'?: string;
	'updated_at'?: string;
	'Published At'?: string;
	'published_at'?: string;
	'Feature Image'?: string;
	'feature_image'?: string;
	excerpt?: string;
	Excerpt?: string;
}

export type SyncStatus = 'synced' | 'different' | 'unknown';

export interface SyncStatusData {
	title: SyncStatus;
	slug: SyncStatus;
	status: SyncStatus;
	tags: SyncStatus;
	featured: SyncStatus;
	created_at: SyncStatus;
	updated_at: SyncStatus;
	published_at: SyncStatus;
	ghostPost?: GhostPost;
}

export interface ParsedArticle {
	frontMatter: ArticleFrontMatter;
	markdownContent: string;
}
