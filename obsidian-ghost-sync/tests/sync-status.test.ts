import { GhostSyncStatusView } from '../src/views/sync-status-view';
import { GhostPost, ArticleFrontMatter, SyncStatus } from '../src/types';

// Mock Obsidian classes
const mockLeaf = {
  view: {},
  on: jest.fn()
} as any;

const mockPlugin = {
  settings: {
    ghostUrl: 'https://test.ghost.io',
    ghostAdminApiKey: 'test_id:secret',
    articlesDir: 'articles'
  }
} as any;

const mockApp = {
  workspace: {
    on: jest.fn(),
    getActiveViewOfType: jest.fn(),
    activeEditor: { file: null },
    getLeavesOfType: jest.fn(() => [])
  },
  vault: {
    on: jest.fn(),
    read: jest.fn()
  }
} as any;

// Mock the view's private methods for testing
class TestableGhostSyncStatusView extends GhostSyncStatusView {
  public testCompareField(ghostValue: any, localValue: any): SyncStatus {
    return (this as any).compareField(ghostValue, localValue);
  }

  public testCompareTags(ghostTags: any[], localTags: string[]): SyncStatus {
    return (this as any).compareTags(ghostTags, localTags);
  }
}

describe('GhostSyncStatusView', () => {
  let view: TestableGhostSyncStatusView;

  beforeEach(() => {
    view = new TestableGhostSyncStatusView(mockLeaf, mockPlugin);
    (view as any).app = mockApp;
  });

  describe('Field Comparison Logic', () => {
    describe('compareField', () => {
      it('should return synced for identical values', () => {
        expect(view.testCompareField('test', 'test')).toBe('synced');
        expect(view.testCompareField(true, true)).toBe('synced');
        expect(view.testCompareField(123, 123)).toBe('synced');
      });

      it('should return different for different values', () => {
        expect(view.testCompareField('test1', 'test2')).toBe('different');
        expect(view.testCompareField(true, false)).toBe('different');
        expect(view.testCompareField(123, 456)).toBe('different');
      });

      it('should handle null/undefined equivalence', () => {
        expect(view.testCompareField(undefined, undefined)).toBe('synced');
        expect(view.testCompareField(null, undefined)).toBe('synced');
        expect(view.testCompareField(undefined, null)).toBe('synced');
      });

      it('should compare dates with tolerance', () => {
        const date1 = '2023-01-01T12:00:00.000Z';
        const date2 = '2023-01-01T12:00:00.500Z'; // 500ms difference
        const date3 = '2023-01-01T12:00:02.000Z'; // 2s difference

        expect(view.testCompareField(date1, date2)).toBe('synced'); // Within 1s tolerance
        expect(view.testCompareField(date1, date3)).toBe('different'); // Beyond tolerance
      });

      it('should handle invalid dates gracefully', () => {
        expect(view.testCompareField('invalid-date', 'also-invalid')).toBe('different');
        expect(view.testCompareField('2023-01-01T12:00:00.000Z', 'invalid')).toBe('different');
      });
    });

    describe('compareTags', () => {
      it('should return synced for identical tag arrays', () => {
        const ghostTags = [{ name: 'tag1' }, { name: 'tag2' }];
        const localTags = ['tag1', 'tag2'];

        expect(view.testCompareTags(ghostTags, localTags)).toBe('synced');
      });

      it('should return different for same tags in different order (order matters for primary tag)', () => {
        const ghostTags = [{ name: 'tag2' }, { name: 'tag1' }];
        const localTags = ['tag1', 'tag2'];

        expect(view.testCompareTags(ghostTags, localTags)).toBe('different');
      });

      it('should return synced for same tags in same order', () => {
        const ghostTags = [{ name: 'tag1' }, { name: 'tag2' }];
        const localTags = ['tag1', 'tag2'];

        expect(view.testCompareTags(ghostTags, localTags)).toBe('synced');
      });

      it('should return different for different tags', () => {
        const ghostTags = [{ name: 'tag1' }, { name: 'tag2' }];
        const localTags = ['tag1', 'tag3'];

        expect(view.testCompareTags(ghostTags, localTags)).toBe('different');
      });

      it('should return different for different tag counts', () => {
        const ghostTags = [{ name: 'tag1' }];
        const localTags = ['tag1', 'tag2'];

        expect(view.testCompareTags(ghostTags, localTags)).toBe('different');
      });

      it('should handle empty tag arrays', () => {
        expect(view.testCompareTags([], [])).toBe('synced');
        expect(view.testCompareTags([{ name: 'tag1' }], [])).toBe('different');
        expect(view.testCompareTags([], ['tag1'])).toBe('different');
      });

      it('should not mutate the original localTags array', () => {
        const ghostTags = [{ name: 'tag1' }, { name: 'tag2' }];
        const localTags = ['tag1', 'tag2']; // Same order
        const originalLocalTags = [...localTags]; // Copy for comparison

        // Call compareTags
        const result = view.testCompareTags(ghostTags, localTags);

        // Should return 'synced' because tags match in same order
        expect(result).toBe('synced');

        // Original array should not be mutated
        expect(localTags).toEqual(originalLocalTags);
        expect(localTags[0]).toBe('tag1'); // Should still be in original order
        expect(localTags[1]).toBe('tag2');
      });
    });
  });

  describe('Sync Status Scenarios', () => {
    const mockGhostPost: GhostPost = {
      id: '123',
      title: 'Test Post',
      slug: 'test-post',
      status: 'published',
      tags: [{ name: 'tag1', slug: 'tag1' }, { name: 'tag2', slug: 'tag2' }],
      featured: false,
      created_at: '2023-01-01T12:00:00.000Z',
      updated_at: '2023-01-01T13:00:00.000Z',
      published_at: '2023-01-01T14:00:00.000Z',
      html: '<p>Test content</p>'
    };

    const mockLocalFrontMatter: ArticleFrontMatter = {
      title: 'Test Post',
      slug: 'test-post',
      status: 'published',
      tags: ['tag1', 'tag2'],
      featured: false,
      created_at: '2023-01-01T12:00:00.000Z',
      updated_at: '2023-01-01T13:00:00.000Z',
      published_at: '2023-01-01T14:00:00.000Z'
    };

    it('should detect fully synced post', () => {
      // All fields match - should be synced
      expect(view.testCompareField(mockGhostPost.title, mockLocalFrontMatter.title)).toBe('synced');
      expect(view.testCompareField(mockGhostPost.slug, mockLocalFrontMatter.slug)).toBe('synced');
      expect(view.testCompareField(mockGhostPost.status, mockLocalFrontMatter.status)).toBe('synced');
      expect(view.testCompareTags(mockGhostPost.tags || [], mockLocalFrontMatter.tags || [])).toBe('synced');
      expect(view.testCompareField(mockGhostPost.featured, mockLocalFrontMatter.featured)).toBe('synced');
    });

    it('should detect title differences', () => {
      expect(view.testCompareField(mockGhostPost.title, 'Different Title')).toBe('different');
    });

    it('should detect status differences', () => {
      expect(view.testCompareField(mockGhostPost.status, 'draft')).toBe('different');
    });

    it('should detect tag differences', () => {
      const differentTags = ['tag1', 'tag3'];
      expect(view.testCompareTags(mockGhostPost.tags || [], differentTags)).toBe('different');
    });

    it('should detect featured flag differences', () => {
      expect(view.testCompareField(mockGhostPost.featured, true)).toBe('different');
    });

    it('should handle missing local fields gracefully', () => {
      // When local frontmatter is missing fields, should compare against defaults
      expect(view.testCompareField(mockGhostPost.status, 'draft')).toBe('different'); // default status
      expect(view.testCompareField(mockGhostPost.featured, false)).toBe('synced'); // default featured
      expect(view.testCompareTags(mockGhostPost.tags || [], [])).toBe('different'); // default empty tags
    });
  });

  describe('Error Handling', () => {
    it('should handle API errors gracefully', () => {
      // Test that view doesn't crash when API calls fail
      // This would be tested with actual API mocking in integration tests
      expect(true).toBe(true); // Placeholder - would need more complex mocking
    });

    it('should handle malformed frontmatter', () => {
      // Test that view handles invalid frontmatter gracefully
      expect(view.testCompareField('valid', undefined)).toBe('different');
      // Note: compareTags expects an array, so we test with empty array instead of undefined
      expect(view.testCompareTags([{ name: 'tag1' }], [])).toBe('different');
    });
  });
});

describe('Sync Status Integration', () => {
  it('should correctly identify authentication failures', () => {
    // This test would catch the 401 authentication bug
    // by verifying that API calls with invalid tokens fail appropriately
    expect(true).toBe(true); // Would need full integration test setup
  });

  it('should handle network failures gracefully', () => {
    // Test that sync status shows "unknown" when network fails
    expect(true).toBe(true); // Would need network mocking
  });

  it('should refresh status after sync operations', () => {
    // Test that status updates after successful sync
    expect(true).toBe(true); // Would need full component testing
  });
});
