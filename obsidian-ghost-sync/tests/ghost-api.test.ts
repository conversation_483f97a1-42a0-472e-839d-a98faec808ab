import { ObsidianGhostAPI } from '../src/api/ghost-api';
import { requestUrl, RequestUrlParam } from 'obsidian';

// Get the mocked requestUrl function
const mockRequestUrl = requestUrl as jest.MockedFunction<typeof requestUrl>;

// Helper function to create proper mock responses
const createMockResponse = (data: any) => ({
  status: data.status || 200,
  headers: {},
  arrayBuffer: new ArrayBuffer(0),
  text: data.text || '',
  json: data.json || {}
});

describe('ObsidianGhostAPI', () => {
  let api: ObsidianGhostAPI;
  const mockUrl = 'https://test.ghost.io';
  const mockKey = 'test_id:0123456789abcdef0123456789abcdef0123456789abcdef0123456789abcdef';

  beforeEach(() => {
    api = new ObsidianGhostAPI(mockUrl, mockKey);
    mockRequestUrl.mockClear();
  });

  describe('JWT Token Generation', () => {
    it('should create valid JWT tokens with hex-decoded secret', async () => {
      // Mock successful response
      mockRequestUrl.mockResolvedValue(createMockResponse({
        status: 200,
        json: { posts: [] }
      }));

      await api.getPosts();

      // Verify requestUrl was called
      expect(mockRequestUrl).toHaveBeenCalledTimes(1);

      const callArgs = mockRequestUrl.mock.calls[0][0] as RequestUrlParam;
      expect(callArgs.headers?.Authorization).toMatch(/^Ghost /);

      // The JWT should be properly formatted (header.payload.signature)
      const token = callArgs.headers!.Authorization.replace('Ghost ', '');
      const parts = token.split('.');
      expect(parts).toHaveLength(3);

      // Decode and verify header (add padding if needed)
      const headerPadded = parts[0] + '='.repeat((4 - parts[0].length % 4) % 4);
      const header = JSON.parse(atob(headerPadded.replace(/-/g, '+').replace(/_/g, '/')));
      expect(header.alg).toBe('HS256');
      expect(header.typ).toBe('JWT');
      expect(header.kid).toBe('test_id');

      // Decode and verify payload (add padding if needed)
      const payloadPadded = parts[1] + '='.repeat((4 - parts[1].length % 4) % 4);
      const payload = JSON.parse(atob(payloadPadded.replace(/-/g, '+').replace(/_/g, '/')));
      expect(payload.aud).toBe('/admin/');
      expect(payload.iat).toBeGreaterThan(0);
      expect(payload.exp).toBeGreaterThan(payload.iat);
    });

    it('should handle invalid API key format', async () => {
      const invalidApi = new ObsidianGhostAPI(mockUrl, 'invalid-key');

      await expect(invalidApi.getPosts()).rejects.toThrow(
        'Invalid Ghost Admin API key format. Expected format: id:secret'
      );
    });

    it('should handle missing API key parts', async () => {
      const invalidApi = new ObsidianGhostAPI(mockUrl, 'only-one-part');

      await expect(invalidApi.getPosts()).rejects.toThrow(
        'Invalid Ghost Admin API key format. Expected format: id:secret'
      );
    });
  });

  describe('API Requests', () => {
    it('should make GET requests with correct headers', async () => {
      mockRequestUrl.mockResolvedValue(createMockResponse({
        status: 200,
        json: { posts: [] }
      }));

      await api.getPosts();

      expect(mockRequestUrl).toHaveBeenCalledWith(
        expect.objectContaining({
          url: expect.stringContaining('/ghost/api/admin/posts/'),
          method: 'GET',
          headers: expect.objectContaining({
            'Authorization': expect.stringMatching(/^Ghost /),
            'Content-Type': 'application/json',
            'Accept-Version': 'v5.0'
          })
        })
      );
    });

    it('should handle 401 authentication errors', async () => {
      mockRequestUrl.mockResolvedValue(createMockResponse({
        status: 401,
        text: 'Unauthorized'
      }));

      await expect(api.getPosts()).rejects.toThrow('Ghost API error: 401 Unauthorized');
    });

    it('should handle 404 not found errors', async () => {
      mockRequestUrl.mockResolvedValue(createMockResponse({
        status: 404,
        text: 'Not Found'
      }));

      await expect(api.getPostBySlug('nonexistent')).rejects.toThrow('Ghost API error: 404 Not Found');
    });

    it('should include query parameters for getPosts', async () => {
      mockRequestUrl.mockResolvedValue(createMockResponse({
        status: 200,
        json: { posts: [] }
      }));

      await api.getPosts({ limit: 50, page: 2 });

      const callArgs = mockRequestUrl.mock.calls[0][0] as RequestUrlParam;
      expect(callArgs.url).toContain('limit=50');
      expect(callArgs.url).toContain('page=2');
      expect(callArgs.url).toContain('include=tags%2Cauthors'); // URL encoded comma
      expect(callArgs.url).toContain('formats=html%2Clexical'); // URL encoded comma
    });
  });

  describe('Post Operations', () => {
    it('should get posts and return array', async () => {
      const mockPosts = [
        { id: '1', title: 'Test Post 1', slug: 'test-post-1' },
        { id: '2', title: 'Test Post 2', slug: 'test-post-2' }
      ];

      mockRequestUrl.mockResolvedValue(createMockResponse({
        status: 200,
        json: { posts: mockPosts }
      }));

      const result = await api.getPosts();
      expect(result).toEqual(mockPosts);
    });

    it('should get post by slug', async () => {
      const mockPost = { id: '1', title: 'Test Post', slug: 'test-post' };

      mockRequestUrl.mockResolvedValue(createMockResponse({
        status: 200,
        json: { posts: [mockPost] }
      }));

      const result = await api.getPostBySlug('test-post');
      expect(result).toEqual(mockPost);
    });

    it('should return null for non-existent post', async () => {
      mockRequestUrl.mockResolvedValue(createMockResponse({
        status: 200,
        json: { posts: [] }
      }));

      const result = await api.getPostBySlug('nonexistent');
      expect(result).toBeNull();
    });

    it('should create new posts', async () => {
      const postData = { title: 'New Post', slug: 'new-post', html: '<p>Content</p>' };
      const createdPost = { id: '123', ...postData };

      mockRequestUrl.mockResolvedValue(createMockResponse({
        status: 201,
        json: { posts: [createdPost] }
      }));

      const result = await api.createPost(postData);
      expect(result).toEqual(createdPost);

      const callArgs = mockRequestUrl.mock.calls[0][0] as RequestUrlParam;
      expect(callArgs.method).toBe('POST');
      expect(callArgs.body).toBe(JSON.stringify({ posts: [postData] }));
    });

    it('should update existing posts', async () => {
      const postData = { id: '123', title: 'Updated Post', slug: 'updated-post' };
      const updatedPost = { ...postData, updated_at: '2023-01-01T00:00:00.000Z' };

      mockRequestUrl.mockResolvedValue(createMockResponse({
        status: 200,
        json: { posts: [updatedPost] }
      }));

      const result = await api.updatePost(postData);
      expect(result).toEqual(updatedPost);

      const callArgs = mockRequestUrl.mock.calls[0][0] as RequestUrlParam;
      expect(callArgs.method).toBe('PUT');
      expect(callArgs.url).toContain('/posts/123/');
      expect(callArgs.body).toBe(JSON.stringify({ posts: [postData] }));
    });
  });

  describe('hexToArrayBuffer', () => {
    it('should convert hex strings to ArrayBuffer correctly', () => {
      // Access private method for testing
      const hexToArrayBuffer = (api as any).hexToArrayBuffer.bind(api);

      const hex = '48656c6c6f'; // "Hello" in hex
      const buffer = hexToArrayBuffer(hex);
      const bytes = new Uint8Array(buffer);

      expect(bytes).toEqual(new Uint8Array([72, 101, 108, 108, 111])); // ASCII values for "Hello"
    });

    it('should handle empty hex strings', () => {
      const hexToArrayBuffer = (api as any).hexToArrayBuffer.bind(api);

      const buffer = hexToArrayBuffer('');
      const bytes = new Uint8Array(buffer);

      expect(bytes.length).toBe(0);
    });
  });
});

// Integration test that would have caught the authentication bug
describe('Ghost API Integration', () => {
  it('should authenticate successfully with valid hex-encoded secret', async () => {
    // This test would fail with the old implementation that didn't decode hex
    const api = new ObsidianGhostAPI(
      'https://test.ghost.io',
      'test_id:0123456789abcdef0123456789abcdef0123456789abcdef0123456789abcdef'
    );

    // Mock successful authentication
    mockRequestUrl.mockResolvedValue(createMockResponse({
      status: 200,
      json: { posts: [] }
    }));

    // This should not throw an authentication error
    await expect(api.getPosts()).resolves.not.toThrow();

    // Verify the Authorization header contains a properly signed JWT
    const callArgs = mockRequestUrl.mock.calls[0][0] as RequestUrlParam;
    expect(callArgs.headers?.Authorization).toMatch(/^Ghost [A-Za-z0-9_-]+\.[A-Za-z0-9_-]+\.[A-Za-z0-9_-]+$/);
  });
});
